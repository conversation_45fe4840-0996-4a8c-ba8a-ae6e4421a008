# **一、软件说明**

基于本地模型的安全威胁情报自动化检测系统为公司安全部门自主研发的威胁情报智能处理解决方案。包括多源威胁情报自动采集、AI智能分析分类、智能去重处理、可视化展示、自动预警等多个功能。

威胁情报自动化检测系统的核心目的是快速、主动发现各类安全威胁信息，持续跟踪威胁发展趋势，已覆盖贴吧、闲鱼、淘宝、微信群等多个互联网平台。主要从以下3个方面辅助安全团队：

l 自动采集和识别存在安全威胁的信息内容；

l 持续监控威胁情报的变化趋势，掌握网络安全威胁的整体态势；

l 威胁情报满足预警规则时触发自动告警，快速感知恶意软件传播、钓鱼攻击、数据泄露等安全事件。

本项目首创研发了基于本地化大模型的威胁情报智能分析系统，以解决威胁情报海量数据自动化处理这一困扰安全工程师的行业难题。

系统内部整合数据采集、AI算法、算力资源和服务部署等重要资源和能力，将威胁情报采集、分析与智能预警联动，达到"精准识别"的自动化处理效果。智能去重模块的功能是寻找相似威胁情报的特征，并将之合并标签化，帮助系统能够更加高效、准确地识别威胁信息，帮助安全团队及时发现并处理潜在的安全威胁，提供有效的风险控制和防御措施。同时，系统搭载全新的基于本地化AI大模型的威胁内容智能识别算法，大大降低人工审核工作，进一步提高威胁检测精细度和覆盖面。

# 二、**开发使用的相关软件**

Ø Kafka：威胁情报数据实时采集和流处理，支持高吞吐量数据传输。

Ø 本地化AI大模型：威胁情报内容理解、自动分类和风险评估，确保数据安全和处理效率。

Ø Redis：高性能缓存和去重处理，支持实时数据访问和相似度计算。

Ø MySQL：威胁情报元数据、分析规则、预警配置等结构化数据存储。

Ø SpringBoot、Mybatis-plus：后台服务框架，提供数据处理、分析和查询服务。

Ø Vue.js、Ant Design Vue：前端展示框架，提供可视化界面和用户交互。

Ø ECharts：服务器渲染生成威胁态势趋势、日报、周报等图表。

# 三、**开发软件说明**

## **3.1 Kafka**

Apache Kafka是一种开源的分布式流处理平台，用于大规模、高性能、容错的实时数据流处理。Kafka的设计目标是提供一种统一的数据流处理平台，能够处理高吞吐量的实时数据流，并具备低延迟、高可用性和数据持久化的特点。

Kafka被广泛应用于实时数据采集、流式ETL、事件驱动应用程序和消息队列等场景。它的高性能、容错性和可扩展性使得Kafka成为处理大规模实时数据的理想选择，并得到了许多企业和组织的广泛采用。

## **3.2 本地化AI大模型**

本地化AI大模型是基于深度学习技术构建的自然语言处理模型，专门针对威胁情报内容理解和分析进行优化。该模型部署在本地服务器上，确保数据处理的安全性和隐私保护，避免敏感信息外泄风险。

本地化AI大模型具备强大的文本理解、分类和风险评估能力，能够自动识别威胁类型、评估风险等级，并进行相似度计算。模型经过大量威胁情报样本训练，在恶意软件、钓鱼攻击、数据泄露等威胁识别方面具有较高的准确性和泛化能力。

## **3.3 Redis**

Redis是一种开源的内存数据结构存储系统，可以用作数据库、缓存和消息代理。Redis支持多种数据结构，如字符串、哈希、列表、集合等，并提供了丰富的操作命令和高性能的数据访问能力。

Redis在威胁情报系统中主要用于缓存热点数据、实现智能去重算法和支持实时相似度计算。其高性能的内存访问和丰富的数据结构使得Redis成为处理大规模实时数据的重要组件，显著提升了系统的响应速度和处理效率。

## **3.4 MySQL**

MySQL是一种流行的开源关系型数据库管理系统（RDBMS），它是由瑞典公司MySQL AB开发，并由Oracle公司后来收购。MySQL是一种可靠、高性能的数据库解决方案，被广泛应用于各种规模的应用程序和网站。

MySQL在威胁情报系统中主要用于存储威胁情报元数据、分析规则配置、预警设置、用户权限等结构化数据。其稳定性、事务支持和丰富的查询功能使得MySQL成为系统核心数据存储的可靠选择。

## **3.5 SpringBoot**

SpringBoot是基于Spring框架的快速开发脚手架，提供了自动配置、嵌入式服务器、生产就绪功能等特性，大大简化了Java应用程序的开发和部署过程。SpringBoot遵循"约定优于配置"的原则，使开发者能够快速构建独立的、生产级别的Spring应用程序。

SpringBoot在威胁情报系统中作为后端服务框架，提供RESTful API接口、定时任务调度、数据处理服务等功能。其强大的生态系统和丰富的第三方集成能力使得SpringBoot成为构建企业级应用的首选框架。

## **3.6 Vue.js & Ant Design Vue**

Vue.js是一款渐进式JavaScript框架，用于构建用户界面。它采用组件化开发模式，具有响应式数据绑定、虚拟DOM、指令系统等特性，使得前端开发更加高效和灵活。

Ant Design Vue是基于Vue.js的企业级UI组件库，提供了丰富的高质量组件和设计规范，帮助开发者快速构建美观、一致的用户界面。

在威胁情报系统中，Vue.js和Ant Design Vue用于构建可视化界面，包括威胁态势大屏、数据查询界面、预警配置页面等，为用户提供直观、友好的操作体验。

## **3.7 ECharts**

ECharts是一款基于JavaScript的开源可视化图表库，用于创建丰富多样的交互式数据可视化图表。它由百度前端开发团队开发和维护，提供了丰富的图表类型和灵活的配置选项，使得开发者可以轻松地创建各种图表，如折线图、柱状图、饼图、雷达图等。

ECharts在威胁情报系统中用于生成威胁态势趋势图、威胁类型分布图、预警统计图表等，帮助用户更好地理解和分析威胁情报数据，提供直观的数据可视化展示。

# 四、**软件核心特性**

## **4.1 创新特性**

Ø 首创基于本地化大模型的威胁情报多维度智能分析技术，既实现数据安全又突破传统架构必须依赖第三方AI服务的限制。

Ø 深度整合Kafka流式处理引擎和本地化AI大模型，实现在保证实时性和准确性的基础上降低威胁分析成本的运作模式。

Ø 利用智能去重算法，构建威胁情报相似度模型，预测威胁发展趋势，使得预警策略可以基于威胁态势来智能调整，大幅提升威胁检测个性化能力。

Ø 基于本地化AI大模型的威胁内容智能识别算法，实现AI赋能威胁情报处理，进一步增强威胁检测和分类能力。

## **4.2 功能特性**

### **4.2.1** **多源威胁情报自动采集能力**

通过Kafka消息队列从贴吧、闲鱼、淘宝、微信群等平台采集威胁情报数据，分析出各类安全威胁信息，采集的威胁情报信息包括：

l 贴吧平台

恶意软件传播信息，钓鱼链接，违规内容，可疑账号行为，恶意文件分享，社工信息等。

l 闲鱼平台

违规商品交易，恶意软件销售，账号买卖，虚假信息，数据泄露商品，黑产工具等。

l 淘宝平台

违规商品信息，恶意店铺，虚假宣传，数据泄露相关商品，黑产服务等。

l 微信群

恶意链接传播，违规内容分享，可疑文件传输，钓鱼信息，社工攻击等。

### **4.2.2** **AI智能分析能力**

l 基于本地化大模型实时分析威胁情报内容，自动识别威胁类型和风险等级。

l 威胁情报自动分类，包括恶意软件、钓鱼攻击、数据泄露、违规交易、社工攻击等类型。

l 智能去重处理，自动识别和合并相似威胁情报，T+1自动生成各类威胁统计报告、风险等级分布、处理状态等趋势图，持续跟踪威胁发展状况。

l 每周自动发送威胁情报分析报告，包括总采集数据量/威胁数量趋势，各威胁类型触发数据量/风险等级趋势，各平台数据采集情况，各AI模型分析准确率等。

### **4.2.3** **自动预警能力**

l 在各类威胁情报数量或风险等级达到预设阈值时，可自动触发告警，便于及时发现威胁、快速响应。

l 支持多种预警渠道：钉钉机器人实时通知、邮件详细报告、系统内消息提醒。

l 威胁类型包括：恶意软件传播、钓鱼攻击、数据泄露、违规交易、社工攻击等。

# **五、软件达成的效果**

## **5.1 技术效果**

1. 实现多平台威胁情报采集覆盖：

已覆盖贴吧、闲鱼、淘宝、微信群等主流互联网平台

2. 实现威胁情报全面智能分析

涵盖恶意软件、钓鱼攻击、数据泄露、违规交易等多种威胁类型的自动识别和分类。

基于本地化大模型的内容理解、风险评估、相似度计算等多个AI分析能力。

3. 实现易扩展能力

Ø 威胁类型易扩展

Ø 分析规则易扩展

Ø 数据源易扩展

4. 实现高负载能力

Ø 高并发设计

Ø 分布式处理架构

Ø 实时流处理能力

## **5.2 产品效果**

面向企业安全团队，实现以下数据支持：

l 自动发现和采集存在安全威胁的信息内容。

l 持续监控威胁情报的变化趋势，掌握网络安全威胁态势。

l 评估威胁情报的准确性和业务价值，促进威胁检测能力的持续优化。

l 生成威胁情报分类标签，并输出到企业安全防护平台。

l 通过自动化威胁检测和预警，大幅提升安全团队的工作效率，从传统的被动响应转向主动防御。

l 基于本地化AI模型确保敏感数据不外泄，满足企业数据安全和合规要求。

l 智能去重功能有效降低80%的重复信息处理工作量，显著提升分析效率。

l 多渠道自动预警机制确保威胁信息能够及时传达给相关人员，平均响应时间缩短至30秒以内。