import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/alarm/alarmNotifyGroup/` + url, ...arg)

/**
 * 告警通知信息分组表Api接口管理器
 *
 * <AUTHOR>
 * @date  2025/04/02 16:24
 **/
export default {
	// 获取告警通知信息分组表分页
	alarmNotifyGroupPage(data) {
		return request('page', data, 'get')
	},
	// 获取告警通知信息分组表列表
	alarmNotifyGroupList(data) {
		return request('list', data, 'get')
	},
	// 提交告警通知信息分组表表单 edit为true时为编辑，默认为新增
	alarmNotifyGroupSubmitForm(data, edit = false) {
		return request(edit ? 'add' : 'edit', data)
	},
	// 删除告警通知信息分组表
	alarmNotifyGroupDelete(data) {
		return request('delete', data)
	},
	// 获取告警通知信息分组表详情
	alarmNotifyGroupDetail(data) {
		return request('detail', data, 'get')
	},
	// 编辑告警通知信息分组状态
	alarmNotifyGroupEditStatus(data) {
		return request('editStatus', data)
	}
}
