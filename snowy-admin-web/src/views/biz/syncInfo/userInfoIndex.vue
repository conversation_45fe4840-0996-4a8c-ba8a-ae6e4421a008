<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form mb-4">
            <a-row :gutter="24">
                <a-col :span="6">
                    <a-form-item label="用户账号" name="userAccount">
                        <a-input v-model:value="searchFormState.userAccount" placeholder="请输入用户账号" />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="姓名" name="userName">
                        <a-input v-model:value="searchFormState.userName" placeholder="请输入姓名" />
                    </a-form-item>
                </a-col>
                <a-col :span="6">
                    <a-form-item label="员工编号" name="employeeNum">
                        <a-input v-model:value="searchFormState.employeeNum" placeholder="请输入员工编号" />
                    </a-form-item>
                </a-col>
                <template v-if="advanced">
                <a-col :span="6">
                    <a-form-item label="用户ID" name="userId">
                        <a-input v-model:value="searchFormState.userId" placeholder="请输入用户ID" />
                    </a-form-item>
                </a-col>
                    <a-col :span="6">
                        <a-form-item label="状态" name="state">
                            <a-input-number v-model:value="searchFormState.state" placeholder="请输入State" style="width: 100%" />
                        </a-form-item>
                    </a-col>
                </template>
                <a-col :span="6">
                    <a-button type="primary" @click="table.refresh(true)">查询</a-button>
                    <a-button style="margin: 0 8px" @click="() => searchFormRef.resetFields()">重置</a-button>
                    <a @click="toggleAdvanced" style="margin-left: 8px">
                        {{ advanced ? '收起' : '展开' }}
                        <component :is="advanced ? 'up-outlined' : 'down-outlined'"/>
                    </a>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="table"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.userId"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection">
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="syncUserInfo()" :loading="submitLoading" v-if="hasPerm('userInfoAdd')">
                        <template #icon><sync-outlined /></template>
                        同步
                    </a-button>
                    <a-button danger @click="deleteBatchUserInfo()" v-if="hasPerm('userInfoBatchDelete')">删除</a-button>
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('userInfoEdit')">编辑</a>
                        <a-divider type="vertical" v-if="hasPerm(['userInfoEdit', 'userInfoDelete'], 'and')" />
                        <a-popconfirm title="确定要删除吗？" @confirm="deleteUserInfo(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('userInfoDelete')">删除</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="table.refresh(true)" />
</template>

<script setup name="userInfo">
    import { message } from 'ant-design-vue'
    import Form from './userInfoForm.vue'
    import userInfoApi from '@/api/biz/userInfoApi'
    let searchFormState = reactive({})
    const searchFormRef = ref()
    const table = ref()
    const formRef = ref()
    const submitLoading = ref(false)
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    // 查询区域显示更多控制
    const advanced = ref(false)
    const toggleAdvanced = () => {
        advanced.value = !advanced.value
    }
    const columns = [
        {
            title: '用户ID',
            dataIndex: 'userId'
        },
        {
            title: '用户账号',
            dataIndex: 'userAccount'
        },
        {
            title: '姓名',
            dataIndex: 'userName'
        },
        {
            title: '员工编号',
            dataIndex: 'employeeNum'
        },
        {
            title: '邮箱',
            dataIndex: 'email1'
        },
        {
            title: '公司名称',
            dataIndex: 'companyOuName'
        },
        {
            title: '部门名称',
            dataIndex: 'ouName'
        },
        {
            title: '领导姓名',
            dataIndex: 'leaderName'
        },
        {
            title: '状态',
            dataIndex: 'state'
        },
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['userInfoEdit', 'userInfoDelete'])) {
        columns.push({
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    let selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        alert: {
            show: false,
            clear: () => {
                selectedRowKeys = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        const searchFormParam = JSON.parse(JSON.stringify(searchFormState))
        return userInfoApi.userInfoPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 删除
    const deleteUserInfo = (record) => {
        let params = [
            {
                userId: record.userId
            }
        ]
        userInfoApi.userInfoDelete(params).then(() => {
            table.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchUserInfo = () => {
        if (selectedRowKeys.value.length < 1) {
            message.warning('请选择一条或多条数据')
            return false
        }
        const params = selectedRowKeys.value.map((m) => {
            return {
                userId: m
            }
        })
        userInfoApi.userInfoDelete(params).then(() => {
            table.value.clearRefreshSelected()
        })
    }
    // 同步公司员工信息
    const syncUserInfo = () => {
		submitLoading.value = true
        userInfoApi.syncUserInfo().then((data) => {
        if (data === true){
        	message.success('同步公司员工信息成功')
            table.value.refresh(true)
        }else{
        	message.error('同步公司员工信息失败')
        }
        }).finally(() => {
        	submitLoading.value = false
        })
    }
</script>
