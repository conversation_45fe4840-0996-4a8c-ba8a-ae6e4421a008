<template>
    <a-drawer
        :title="formData.policyId ? '编辑策略' : '增加策略'"
        :width="600"
        :visible="visible"
        :destroy-on-close="true"
        :footer-style="{ textAlign: 'right' }"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-form-item label="策略名称：" name="policyName">
                <a-input v-model:value="formData.policyName" placeholder="请输入策略名称" />
            </a-form-item>
            <a-form-item label="数据类型：" name="dataType">
                <a-select v-model:value="formData.dataType" placeholder="请选择数据类型" :options="dataTypeOptions" />
            </a-form-item>
            <a-form-item label="应用ID：" name="appId">
                <a-select v-model:value="formData.appId" placeholder="请选择应用ID" :options="appIdOptions" @change="appIdOrSdkVersionChange"/>
            </a-form-item>
            <a-form-item label="SDK版本号：" name="sdkVersion">
                <a-select v-model:value="formData.sdkVersion" placeholder="请选择SDK版本号" :options="sdkVersionOptions" @change="appIdOrSdkVersionChange"/>
            </a-form-item>
            <a-form-item label="策略种类ID：" name="policyClassId">
                <a-select v-model:value="formData.policyClassId" placeholder="请选择策略种类ID" :options="policyClassIdOptions" optionFilterProp="policyClassNameZh" :field-names="{ label: 'policyClassNameZh', value: 'policyClassId' }" show-search allow-clear/>
            </a-form-item>
            <a-form-item name="templateId">
				<template #label>
					<a-tooltip>
						<template #title> 通过模板生成规则DRL代码，选择模板并填入参数后点击下方【生成】按钮生成！ </template>
						<question-circle-outlined />
					</a-tooltip>
					&nbsp 模板：
				</template>
                <a-select v-model:value="formData.templateId" placeholder="请选择模板" :options="templateIdOptions" optionFilterProp="templateName" :field-names="{ label: 'templateName', value: 'templateId' }" show-search allow-clear @change="templateIdChange"/>
            </a-form-item>

  <a-alert v-model:message="templateData.description" type="success" closable v-if="templateData.description"/>

  <div>
    <snowy-form-build
      :value="jsonKFBData"
      ref="KFB"
      @submit="getKFBData"/>
    <a-button @click="getKFBData" danger>生成</a-button>
  </div>

            <a-form-item label="规则条件DRL：" name="ruleConditionDrl">
                <a-textarea v-model:value="formData.ruleConditionDrl" placeholder="请输入规则条件DRL" :auto-size="{ minRows: 3, maxRows: 30 }" />
            </a-form-item>
            <a-form-item label="允许最大设备数(0表示不限制)：" name="deviceMaxNum" v-if="!formData.policyId">
                <a-input-number v-model:value="formData.deviceMaxNum" placeholder="请输入允许最大设备数(0表示不限制)" :min="0" :max="100" :defaultValue="30" style="width: 100%" />
            </a-form-item>
            <a-form-item label="描述：" name="description">
                <a-textarea v-model:value="formData.description" placeholder="请输入描述" :auto-size="{ minRows: 3, maxRows: 30 }" />
            </a-form-item>
            <a-form-item label="状态：" name="status">
                <a-select v-model:value="formData.status" placeholder="请选择状态" :options="statusOptions" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
        </template>
    </a-drawer>
</template>

<script setup name="policyForm">
	import { onUpdated } from 'vue'
    import tool from '@/utils/tool'
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import policyApi from '@/api/biz/policyApi'
    import policyClassApi from '@/api/biz/policyClassApi'
    import templateApi from '@/api/biz/templateApi'
    // 抽屉状态
    const visible = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    const dataTypeOptions = ref([])
    const policyClassIdOptions = ref([])
    const templateIdOptions = ref([])
    const templateData = ref({})
    const jsonKFBData = ref({})
    const KFB = ref()

    const statusOptions = ref([])
    const appIdOptions = ref([])
    const sdkVersionOptions = ref([])

    // 打开抽屉
    const onOpen = (record) => {
        visible.value = true
        dataTypeOptions.value = tool.dictList('data_type')
        appIdOptions.value = tool.dictList('cloud_controller_app_id')
        sdkVersionOptions.value = tool.dictList('cloud_controller_sdk_version')

        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
            // 解决数值类型无法回显问题
            if(formData.value.appId){
            	formData.value.appId = formData.value.appId.toString();
            }
        }else{
			// 默认选中第一条
			formData.value.dataType = dataTypeOptions.value[1].value;
			formData.value.appId = appIdOptions.value[0].value;
 			formData.value.sdkVersion = sdkVersionOptions.value[0].value;
 			formData.value.deviceMaxNum = 30;
        }
        statusOptions.value = tool.dictList('status')
//        appIdOptions.value = tool.dictList('cloud_controller_app_id')
//        sdkVersionOptions.value = tool.dictList('cloud_controller_sdk_version')

        loadPolicyClassData();
        loadTemplateList();
        // 此处调用回显的话可能组件还没有渲染完成，无法正常回显，所以在onUpdated()方法中调用
       // setKFBData();
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        visible.value = false
        templateData.value = {};
    	jsonKFBData.value = {};
    	KFB.value.setData({});
    }
    // 默认要校验的
    const formRules = {
        policyName: [required('请输入策略名')],
        policyClassId: [required('请输入策略种类ID')],
        dataType: [required('请输入数据类型')],
        appId: [required('请选择应用ID')],
        sdkVersion: [required('请选择SDK版本号')],
    }
    // 验证并提交数据
    const onSubmit = () => {
        formRef.value
            .validate()
            .then(() => {
                submitLoading.value = true
                const formDataParam = cloneDeep(formData.value)
                policyApi
                    .policySubmitForm(formDataParam, !formDataParam.policyId)
                    .then(() => {
                        onClose()
                        emit('successful')
                    })
                    .finally(() => {
                        submitLoading.value = false
                    })
            })
    }
    // 应用ID或SDK版本将清空已选择的policyClassId并重新获取数据
    const appIdOrSdkVersionChange =() => {
    	formData.value.policyClassId = undefined;
        loadPolicyClassData();
    }
    // 获取PolicyClass列表
    const loadPolicyClassData = (parameter) => {
        let params = {
        	appId: formData.value.appId,
        	sdkVersion: formData.value.sdkVersion
            }
        policyClassApi.policyClassList(params).then((data) => {
        if(data){
            data.map((item) => {
				item.policyClassNameZh=item.policyClassId + '-' + item.policyClassName
            	})
        }
            policyClassIdOptions.value = data
        })
    }
    // 获取模板列表
    const loadTemplateList = (parameter) => {
        templateApi.templateList(parameter).then((data) => {
            templateIdOptions.value = data;
        })
    }
    // 选择模板
    const templateIdChange = (templateId) => {
            let params = {
                    templateId: templateId
                }
        templateApi.templateDetail(params).then((data) => {
            jsonKFBData.value = JSON.parse(data.uiJson);
            templateData.value = data;
        })
    }
    // 获取表单设计器数据
    const getKFBData = () => {
       // 通过函数获取数据
       KFB.value.getData().then(res => {
         // 获取数据成功
       //  alert(JSON.stringify(res))
            let params = {
                    template: templateData.value.template,
                    dataJson: res
                }
        templateApi.templatePreview(params).then((data) => {
            formData.value.ruleConditionDrl = data;
            let ruleConditionJson = {
                    templateData: templateData.value,
                    dataJson: res
                }
            formData.value.ruleConditionJson = JSON.stringify(ruleConditionJson);
        })
       })
         .catch(err => {
           console.log(err, '校验失败')
         })
     }
    // 设置表单设计器数据(回显)
    const setKFBData = () => {
    if(formData.value.policyId && formData.value.ruleConditionJson){
    	let ruleConditionJson =JSON.parse(formData.value.ruleConditionJson)
    	if(ruleConditionJson.templateData){
        	jsonKFBData.value = JSON.parse(ruleConditionJson.templateData.uiJson);
        	if(ruleConditionJson.dataJson){
            	KFB.value.setData(ruleConditionJson.dataJson);
            }
        	templateData.value = ruleConditionJson.templateData;
        	formData.value.templateId = ruleConditionJson.templateData.templateId;
    	}
     }
     }
   onUpdated(() => {
		setKFBData();
    })
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
