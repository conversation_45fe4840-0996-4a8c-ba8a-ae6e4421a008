<template>
    <a-drawer
        :title="formData.id ? '编辑样本采集数据' : '增加样本采集数据'"
        :width="600"
        :visible="visible"
        :destroy-on-close="true"
        :footer-style="{ textAlign: 'right' }"
        @close="onClose"
    >
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-form-item label="规则英文名：" name="ruleNameEn">
                <a-input v-model:value="formData.ruleNameEn" placeholder="请输入规则英文名" allow-clear />
            </a-form-item>
            <a-form-item label="用户ID：" name="userId">
                <a-input v-model:value="formData.userId" placeholder="请输入用户ID" allow-clear />
            </a-form-item>
            <a-form-item label="IP地址：" name="ip">
                <a-input v-model:value="formData.ip" placeholder="请输入IP地址" allow-clear />
            </a-form-item>
            <a-form-item label="HostName：" name="hostName">
                <a-input v-model:value="formData.hostName" placeholder="请输入HostName" allow-clear />
            </a-form-item>
            <a-form-item label="进程名称：" name="processNames">
                <a-textarea v-model:value="formData.processNames" placeholder="请输入进程名称" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-item>
            <a-form-item label="所有用户ID：" name="allUserIds">
                <a-textarea v-model:value="formData.allUserIds" placeholder="请输入所有用户ID" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-item>
            <a-form-item label="数据：" name="data">
                <a-textarea v-model:value="formData.data" placeholder="请输入数据" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-item>
            <a-form-item label="状态：" name="status">
                <a-select v-model:value="formData.status" placeholder="请选择状态" :options="statusOptions" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
        </template>
    </a-drawer>
</template>

<script setup name="sampleCollectionDataForm">
    import tool from '@/utils/tool'
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import sampleCollectionDataApi from '@/api/biz/sampleCollectionDataApi'
    // 抽屉状态
    const visible = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    const statusOptions = ref([])

    // 打开抽屉
    const onOpen = (record) => {
        visible.value = true
        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
        }
        statusOptions.value = tool.dictList('status')
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        visible.value = false
    }
    // 默认要校验的
    const formRules = {
        status: [required('请输入状态')],
    }
    // 验证并提交数据
    const onSubmit = () => {
        formRef.value
            .validate()
            .then(() => {
                submitLoading.value = true
                const formDataParam = cloneDeep(formData.value)
                sampleCollectionDataApi
                    .sampleCollectionDataSubmitForm(formDataParam, !formDataParam.id)
                    .then(() => {
                        onClose()
                        emit('successful')
                    })
                    .finally(() => {
                        submitLoading.value = false
                    })
            })
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
