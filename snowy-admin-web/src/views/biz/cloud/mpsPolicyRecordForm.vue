<template>
    <a-drawer
        :title="formData.recordId ? '编辑MPS策略下发记录' : '增加MPS策略下发记录'"
        :width="600"
        :visible="visible"
        :destroy-on-close="true"
        :footer-style="{ textAlign: 'right' }"
        @close="onClose">
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-form-item label="设备ID：" name="deviceId">
                <a-input v-model:value="formData.deviceId" placeholder="请输入设备ID" allow-clear />
            </a-form-item>
            <a-form-item label="应用ID：" name="appId">
                <a-input v-model:value="formData.appId" placeholder="请输入应用ID" allow-clear />
            </a-form-item>
            <a-form-item label="SDK版本号：" name="sdkVersion">
                <a-input v-model:value="formData.sdkVersion" placeholder="请输入SDK版本号" allow-clear />
            </a-form-item>
            <a-form-item label="策略ID：" name="policyId">
                <a-input v-model:value="formData.policyId" placeholder="请输入策略ID" allow-clear />
            </a-form-item>
            <a-form-item label="标签ID：" name="tagId">
                <a-input v-model:value="formData.tagId" placeholder="请输入标签ID" allow-clear />
            </a-form-item>
            <a-form-item label="标签优先级：" name="tagLevel">
                <a-input v-model:value="formData.tagLevel" placeholder="请输入标签优先级" allow-clear />
            </a-form-item>
            <a-form-item label="数据：" name="dataJson">
                <a-input v-model:value="formData.dataJson" placeholder="请输入数据" allow-clear />
            </a-form-item>
            <a-form-item label="限制时间(秒)：" name="timeLimit">
                <a-input v-model:value="formData.timeLimit" placeholder="请输入限制时间(秒)" allow-clear />
            </a-form-item>
            <a-form-item label="描述：" name="description">
                <a-input v-model:value="formData.description" placeholder="请输入描述" allow-clear />
            </a-form-item>
            <a-form-item label="回传状态：" name="receivedStatus">
                <a-select v-model:value="formData.receivedStatus" placeholder="请选择回传状态" :options="receivedStatusOptions" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
        </template>
    </a-drawer>
</template>

<script setup name="mpsPolicyRecordForm">
    import tool from '@/utils/tool'
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import mpsPolicyRecordApi from '@/api/biz/mpsPolicyRecordApi'
    // 抽屉状态
    const visible = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    const receivedStatusOptions = ref([])

    // 打开抽屉
    const onOpen = (record) => {
        visible.value = true
        if (record) {
            let recordData = cloneDeep(record)
            formData.value = Object.assign({}, recordData)
        }
        receivedStatusOptions.value  = [
                                                                    {
                                                                        "value": 0,
                                                                        "label": "未完成"
                                                                    },
                                                                    {
                                                                        "value": 1,
                                                                        "label": "已完成"
                                                                    },
                                                                    {
                                                                        "value": 2,
                                                                        "label": "已超时"
                                                                    }
                                                                ]
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        visible.value = false
    }
    // 默认要校验的
    const formRules = {
    }
    // 验证并提交数据
    const onSubmit = () => {
        formRef.value
            .validate()
            .then(() => {
                submitLoading.value = true
                const formDataParam = cloneDeep(formData.value)
                mpsPolicyRecordApi
                    .mpsPolicyRecordSubmitForm(formDataParam, !formDataParam.recordId)
                    .then(() => {
                        onClose()
                        emit('successful')
                    })
                    .finally(() => {
                        submitLoading.value = false
                    })
            })
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
