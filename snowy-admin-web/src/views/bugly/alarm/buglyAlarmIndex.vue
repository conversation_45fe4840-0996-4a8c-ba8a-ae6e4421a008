<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form mb-4">
            <a-row :gutter="24">
                <a-col :span="6">
                    <a-form-item label="告警名称" name="alarmName">
                        <a-input v-model:value="searchFormState.alarmName" placeholder="请输入告警名称" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="告警周期" name="alarmType">
                        <a-select v-model:value="searchFormState.alarmType" placeholder="请选择告警周期" :options="alarmTypeOptions" />
                    </a-form-item>
                </a-col>
                    <a-col :span="4">
                        <a-form-item label="应用ID" name="appId">
                            <a-select v-model:value="searchFormState.appId" placeholder="请选择应用ID" optionFilterProp="appNameZh" :options="appIdOptions" :field-names="{ label: 'appNameZh', value: 'appId' }" show-search allow-clear/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="4">
                        <a-form-item label="状态" name="status">
                            <a-select v-model:value="searchFormState.status" placeholder="请选择状态" :options="statusOptions" allow-clear/>
                        </a-form-item>
                    </a-col>
                <a-col :span="6">
                    <a-button type="primary" @click="table.refresh(true)">查询</a-button>
                    <a-button style="margin: 0 8px" @click="() => searchFormRef.resetFields()">重置</a-button>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="table"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection">
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('buglyAlarmAdd')">
                        <template #icon><plus-outlined /></template>
                        新增
                    </a-button>
                    <a-button danger @click="deleteBatchBuglyAlarm()" v-if="hasPerm('buglyAlarmBatchDelete')">删除</a-button>
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'alarmType'">
                    {{ $TOOL.dictTypeData('bugly_monitor_alarm_type', record.alarmType) }}
                </template>
                <template v-if="column.dataIndex === 'notifyType'">
                    <a-tag v-for="textValue in JSON.parse(record.notifyType)" :key="textValue" color="green">{{ $TOOL.dictTypeData('bugly_monitor_notify_type', textValue) }}</a-tag>
                </template>
                <template v-if="column.dataIndex === 'status'">
                	<a-switch
                		:loading="statusLoading"
                		:checked="record.status === '1'"
                		@change="editStatus(record)"
                		checkedChildren="开启"
                		unCheckedChildren="关闭"
                		/>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('buglyAlarmEdit')">编辑</a>
                        <a-divider type="vertical" v-if="hasPerm(['buglyAlarmEdit', 'buglyAlarmDelete'], 'and')" />
                        <a-popconfirm title="确定要删除吗？" @confirm="deleteBuglyAlarm(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('buglyAlarmDelete')">删除</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="table.refresh(true)" />
</template>

<script setup name="buglyAlarmIndex">
    import { message } from 'ant-design-vue'
    import tool from '@/utils/tool'
    import Form from './buglyAlarmForm.vue'
    import buglyAlarmApi from '@/api/bugly/buglyAlarmApi'
    import appInfoApi from '@/api/bugly/appInfoApi'
    let searchFormState = reactive({})
    const searchFormRef = ref()
    const table = ref()
    const formRef = ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    const statusLoading = ref(false)
    const appIdOptions = ref([])
    // 查询区域显示更多控制
    const advanced = ref(false)
    const columns = [
        {
            title: '告警名称',
            dataIndex: 'alarmName'
        },
        {
            title: '告警周期',
            dataIndex: 'alarmType'
        },
        {
            title: '接收方式',
            dataIndex: 'notifyType'
        },
        {
            title: '应用',
            dataIndex: 'appId'
        },
        {
            title: '描述',
            dataIndex: 'description'
        },
        {
            title: '创建时间',
            dataIndex: 'createTime'
        },
        {
            title: '状态',
            dataIndex: 'status'
        }
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['buglyAlarmEdit', 'buglyAlarmDelete'])) {
        columns.push({
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    let selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        alert: {
            show: false,
            clear: () => {
                selectedRowKeys = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
    	loadAppInfoData();
        const searchFormParam = JSON.parse(JSON.stringify(searchFormState))
        return buglyAlarmApi.buglyAlarmPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 删除
    const deleteBuglyAlarm = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        buglyAlarmApi.buglyAlarmDelete(params).then(() => {
            table.value.refresh(true)
        })
    }
    	// 修改状态
    	const editStatus = (record) => {
    		statusLoading.value = true
        	let params = {
                	id: record.id,
                	status: record.status === '1' ? '0' : '1'
            	}
    		buglyAlarmApi.alarmEditStatus(params)
    			.then(() => {
    				table.value.refresh()
    			}).finally(() => {
    				statusLoading.value = false
    			})
    	}
    // 批量删除
    const deleteBatchBuglyAlarm = () => {
        if (selectedRowKeys.value.length < 1) {
            message.warning('请选择一条或多条数据')
            return false
        }
        const params = selectedRowKeys.value.map((m) => {
            return {
                id: m
            }
        })
        buglyAlarmApi.buglyAlarmDelete(params).then(() => {
            table.value.clearRefreshSelected()
        })
    }
    const alarmTypeOptions = tool.dictList('bugly_monitor_alarm_type')
    const notifyTypeOptions = tool.dictList('bugly_monitor_notify_type')
    const statusOptions = tool.dictList('status')
    // 获取应用信息表列表
    const loadAppInfoData = (parameter) => {
        appInfoApi.appInfoList(parameter).then((data) => {
        if(data){
            data.map((item) => {
            item.platformZh = item.platform+'未知类型';
            if(item.platform === 0 ){
            	item.platformZh = 'Android';
            }else if(item.platform === 1 ){
            	item.platformZh = 'iOS';
            }else if(item.platform === 3 ){
            	item.platformZh = 'H5';
            }

				item.appNameZh=item.appName + '(' + item.platformZh + ')'
            	})
        }
            appIdOptions.value = data
        })
    }
</script>
