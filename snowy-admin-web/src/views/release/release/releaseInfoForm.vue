<template>
    <a-drawer
        :title="formData.id ? '编辑灰度基本信息' : '增加灰度基本信息'"
        :width="600"
        :visible="visible"
        :destroy-on-close="true"
        :footer-style="{ textAlign: 'right' }"
        @close="onClose">
        <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
            <a-form-item label="灰度名称：" name="releaseName">
                <a-input v-model:value="formData.releaseName" placeholder="请输入灰度名称" allow-clear />
            </a-form-item>
            <a-form-item label="应用ID：" name="appId">
                <a-input v-model:value="formData.appId" placeholder="请输入应用ID，如：1001" allow-clear />
            </a-form-item>
            <a-form-item label="云控模板ID：" name="templateId">
                <a-input v-model:value="formData.templateId" placeholder="请输入云控模板ID，如：1001103" allow-clear />
            </a-form-item>
            <a-form-item label="配置版本号：" name="configVersion">
                <a-input v-model:value="formData.configVersion" placeholder="请输入配置版本号，如：v202411181140" allow-clear />
            </a-form-item>

            <!--
            <a-form-item label="Lua任务名：" name="luaTask">
                <a-input v-model:value="formData.luaTask" placeholder="请输入Lua任务名" allow-clear />
            </a-form-item>
            -->

            <a-form-item label="使用的云控配置版本：" name="useCloudControllerConfigVersion">
                <a-checkbox-group v-model:value="formData.useCloudControllerConfigVersion" placeholder="请选择使用的云控配置版本" :options="useCloudControllerConfigVersionOptions" />
            </a-form-item>
            <a-form-item label="时间类型：" name="timeType">
                <a-select v-model:value="formData.timeType" placeholder="请选择时间类型" :options="timeTypeOptions" />
            </a-form-item>
            <a-form-item label="接收方式：" name="notifyType">
                <a-checkbox-group v-model:value="formData.notifyType" placeholder="请选择接收方式" :options="notifyTypeOptions" />
            </a-form-item>
            <a-form-item label="接收人：" name="receiveUsers">
                <a-select v-model:value="formData.receiveUsers" placeholder="请选择接收人" mode="multiple" optionFilterProp="userNameZh" :options="userOptions" :field-names="{ label: 'userNameZh', value: 'userIdZh' }" show-search allow-clear />
            </a-form-item>
            <a-form-item label="抄送人：" name="ccUsers">
                <a-select v-model:value="formData.ccUsers" placeholder="请选择抄送人" mode="multiple" optionFilterProp="userNameZh" :options="userOptions" :field-names="{ label: 'userNameZh', value: 'userIdZh' }" show-search allow-clear />
            </a-form-item>
            <a-form-item label="灰度状态：" name="releaseStatus">
                <a-select v-model:value="formData.releaseStatus" placeholder="请选择灰度状态" :options="releaseStatusOptions" />
            </a-form-item>
            <a-form-item label="灰度内容：" name="releaseContent">
                <a-textarea v-model:value="formData.releaseContent" placeholder="请输入灰度内容" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-item>
            <a-form-item label="描述：" name="description">
                <a-textarea v-model:value="formData.description" placeholder="请输入描述" :auto-size="{ minRows: 3, maxRows: 5 }" />
            </a-form-item>
            <a-form-item label="状态：" name="status">
                <a-select v-model:value="formData.status" placeholder="请选择状态" :options="statusOptions" />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
            <a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
        </template>
    </a-drawer>
</template>

<script setup name="releaseInfoForm">
    import tool from '@/utils/tool'
    import { cloneDeep } from 'lodash-es'
    import { required } from '@/utils/formRules'
    import releaseInfoApi from '@/api/release/releaseInfoApi'
    import userInfoApi from '@/api/biz/userInfoApi'
    // 抽屉状态
    const visible = ref(false)
    const emit = defineEmits({ successful: null })
    const formRef = ref()
    // 表单数据
    const formData = ref({})
    const submitLoading = ref(false)
    const timeTypeOptions = ref([])
    const notifyTypeOptions = ref([])
    const releaseStatusOptions = ref([])
    const statusOptions = ref([])
    const useCloudControllerConfigVersionOptions = ref([])
    const userOptions = ref([])

    // 打开抽屉
    const onOpen = (record) => {
        visible.value = true
        if (record) {
            let recordData = cloneDeep(record)
            recordData.notifyType = JSON.parse(recordData.notifyType)
            recordData.useCloudControllerConfigVersion = JSON.parse(recordData.useCloudControllerConfigVersion)
            recordData.receiveUsers = JSON.parse(recordData.receiveUser)
            recordData.ccUsers = JSON.parse(recordData.ccUser)
            formData.value = Object.assign({}, recordData)
        }
        timeTypeOptions.value = tool.dictList('mps_monitor_alarm_type')
        notifyTypeOptions.value = tool.dictList('bugly_monitor_notify_type')
        releaseStatusOptions.value = tool.dictList('release_mps_release_status')
        statusOptions.value = tool.dictList('status')
        useCloudControllerConfigVersionOptions.value = tool.dictList('release_mps_use_cloud_controller_config_version')

        loadUsersData();
    }
    // 关闭抽屉
    const onClose = () => {
        formRef.value.resetFields()
        formData.value = {}
        userOptions.value = []
        visible.value = false
    }
    // 默认要校验的
    const formRules = {
        releaseName: [required('请输入灰度名称')],
        appId: [required('请输入应用ID')],
        templateId: [required('请输入云控模板ID')],
        configVersion: [required('请输入配置版本号')],
        useCloudControllerConfigVersion: [required('请选择使用的云控配置版本')],
      //  releaseContent: [required('请输入灰度内容')],
      //  description: [required('请输入描述')],
    }
    // 验证并提交数据
    const onSubmit = () => {
        formRef.value
            .validate()
            .then(() => {
                submitLoading.value = true
                const formDataParam = cloneDeep(formData.value)
                formDataParam.notifyType = JSON.stringify(formDataParam.notifyType)
                formDataParam.useCloudControllerConfigVersion = JSON.stringify(formDataParam.useCloudControllerConfigVersion)
                releaseInfoApi
                    .releaseInfoSubmitForm(formDataParam, !formDataParam.id)
                    .then(() => {
                        onClose()
                        emit('successful')
                    })
                    .finally(() => {
                        submitLoading.value = false
                    })
            })
    }
    // 获取公司员工列表
    const loadUsersData = (parameter) => {
        userInfoApi.userInfoList2(parameter).then((data) => {
            userOptions.value = data
        })
    }
    // 抛出函数
    defineExpose({
        onOpen
    })
</script>
