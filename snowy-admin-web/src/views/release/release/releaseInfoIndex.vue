<template>
    <a-card :bordered="false">
        <a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form mb-4">
            <a-row :gutter="24">
                <a-col :span="5">
                    <a-form-item label="灰度名称" name="releaseName">
                        <a-input v-model:value="searchFormState.releaseName" placeholder="请输入灰度名称" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="时间类型" name="timeType">
                        <a-select v-model:value="searchFormState.timeType" placeholder="请选择时间类型" :options="timeTypeOptions" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="灰度状态" name="releaseStatus">
                        <a-select v-model:value="searchFormState.releaseStatus" placeholder="请选择灰度状态" :options="releaseStatusOptions" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="灰度内容" name="releaseContent">
                        <a-input v-model:value="searchFormState.releaseContent" placeholder="请输入灰度内容" />
                    </a-form-item>
                </a-col>
                <a-col :span="4">
                    <a-form-item label="状态" name="status">
                        <a-select v-model:value="searchFormState.status" placeholder="请选择状态" :options="statusOptions" />
                    </a-form-item>
                </a-col>
                <a-col :span="3">
                    <a-button type="primary" @click="table.refresh(true)">查询</a-button>
                    <a-button style="margin: 0 8px" @click="() => searchFormRef.resetFields()">重置</a-button>
                </a-col>
            </a-row>
        </a-form>
        <s-table
            ref="table"
            :columns="columns"
            :data="loadData"
            :alert="options.alert.show"
            bordered
            :row-key="(record) => record.id"
            :tool-config="toolConfig"
            :row-selection="options.rowSelection">
            <template #operator class="table-operator">
                <a-space>
                    <a-button type="primary" @click="formRef.onOpen()" v-if="hasPerm('releaseInfoAdd')">
                        <template #icon><plus-outlined /></template>
                        新增
                    </a-button>
                    <a-button danger @click="deleteBatchReleaseInfo()" v-if="hasPerm('releaseInfoBatchDelete')">删除</a-button>
                </a-space>
            </template>
            <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'useCloudControllerConfigVersion'">
                    <a-tag v-for="textValue in JSON.parse(record.useCloudControllerConfigVersion)" :key="textValue" color="green" style="margin-bottom: 4px">{{ $TOOL.dictTypeData('release_mps_use_cloud_controller_config_version', textValue) }}</a-tag>
                </template>
                <template v-if="column.dataIndex === 'timeType'">
                    {{ $TOOL.dictTypeData('mps_monitor_alarm_type', record.timeType) }}
                </template>
                <template v-if="column.dataIndex === 'notifyType'">
                    <a-tag v-for="textValue in JSON.parse(record.notifyType)" :key="textValue" color="blue" style="margin-bottom: 4px">{{ $TOOL.dictTypeData('bugly_monitor_notify_type', textValue) }}</a-tag>
                </template>
                <template v-if="column.dataIndex === 'receiveUser'">
                  <div v-if="record.receiveUser && (JSON.parse(record.receiveUser)).length > 0">
                    <a-tag v-for="textValue in JSON.parse(record.receiveUser)" :key="textValue" color="blue" style="margin-bottom: 4px">{{ textValue }}</a-tag>
                  </div>
                  <div v-else></div>
                </template>
                <template v-if="column.dataIndex === 'ccUser'">
                  <div v-if="record.ccUser && (JSON.parse(record.ccUser)).length > 0">
                    <a-tag v-for="textValue in JSON.parse(record.ccUser)" :key="textValue" color="blue" style="margin-bottom: 4px">{{ textValue }}</a-tag>
                  </div>
                  <div v-else></div>
                </template>
                <template v-if="column.dataIndex === 'releaseStatus'">
					<a-tag v-if="record.releaseStatus === '0'" color="#606d80">
						{{ $TOOL.dictTypeData('release_mps_release_status', record.releaseStatus) }}
					</a-tag>
					<a-tag v-else-if="record.releaseStatus === '1'" color="#87d068">
						{{ $TOOL.dictTypeData('release_mps_release_status', record.releaseStatus) }}
					</a-tag>
					<a-tag v-else-if="record.releaseStatus === '2'" color="#f50">
						{{ $TOOL.dictTypeData('release_mps_release_status', record.releaseStatus) }}
					</a-tag>
					<a-tag v-else-if="record.releaseStatus === '3'" color="#108ee9">
						{{ $TOOL.dictTypeData('release_mps_release_status', record.releaseStatus) }}
					</a-tag>
					<a-tag v-else-if="record.releaseStatus === '4'" color="#f50">
						{{ $TOOL.dictTypeData('release_mps_release_status', record.releaseStatus) }}
					</a-tag>
					<a-tag v-else color="default">
						{{ $TOOL.dictTypeData('release_mps_release_status', record.releaseStatus) }}
					</a-tag>
                </template>
                <template v-if="column.dataIndex === 'status'">
					<a-tag v-if="record.status === '1'" color="#87d068">
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
					<a-tag v-else-if="record.status === '0'" color="#f50">
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
					<a-tag v-else-if="record.status === '-1'" color="#108ee9">
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
					<a-tag v-else>
						{{ $TOOL.dictTypeData('status', record.status) }}
					</a-tag>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <a-space>
                        <a @click="formRef.onOpen(record)" v-if="hasPerm('releaseInfoEdit')">编辑</a>
                        <a-divider type="vertical" v-if="hasPerm(['releaseInfoEdit', 'releaseInfoDelete'], 'and')" />
                        <a-popconfirm title="确定要删除吗？" @confirm="deleteReleaseInfo(record)">
                            <a-button type="link" danger size="small" v-if="hasPerm('releaseInfoDelete')">删除</a-button>
                        </a-popconfirm>
                    </a-space>
                </template>
            </template>
        </s-table>
    </a-card>
    <Form ref="formRef" @successful="table.refresh(true)" />
</template>

<script setup name="releaseInfoIndex">
    import { message } from 'ant-design-vue'
    import tool from '@/utils/tool'
    import Form from './releaseInfoForm.vue'
    import releaseInfoApi from '@/api/release/releaseInfoApi'
    let searchFormState = reactive({})
    const searchFormRef = ref()
    const table = ref()
    const formRef = ref()
    const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
    // 查询区域显示更多控制
    const advanced = ref(false)
    const toggleAdvanced = () => {
        advanced.value = !advanced.value
    }
    const columns = [
        {
            title: '灰度名称',
            dataIndex: 'releaseName'
        },
        {
            title: '应用ID',
            dataIndex: 'appId'
        },
        {
            title: '云控模板ID',
            dataIndex: 'templateId'
        },
        {
            title: '配置版本号',
            dataIndex: 'configVersion'
        },
     //   {
      //      title: 'Lua任务名',
      //      dataIndex: 'luaTask'
      //  },
        {
            title: '使用的云控配置版本',
            dataIndex: 'useCloudControllerConfigVersion'
        },
        {
            title: '时间类型',
            dataIndex: 'timeType'
        },
        {
            title: '接收方式',
            dataIndex: 'notifyType'
        },
        {
            title: '接收人',
            dataIndex: 'receiveUser'
        },
        {
            title: '抄送人',
            dataIndex: 'ccUser'
        },
        {
            title: '灰度状态',
            dataIndex: 'releaseStatus'
        },
        {
            title: '灰度内容',
            dataIndex: 'releaseContent'
        },
     //   {
     //       title: '描述',
      //      dataIndex: 'description',
     //             ellipsis: true
     //   },
        {
            title: '创建人',
            dataIndex: 'createUserName'
        },
        {
            title: '更新人',
            dataIndex: 'updateUserName'
        },
        {
            title: '创建时间',
            dataIndex: 'createTime'
        },
        {
            title: '状态',
            dataIndex: 'status'
        },
    ]
    // 操作栏通过权限判断是否显示
    if (hasPerm(['releaseInfoEdit', 'releaseInfoDelete'])) {
        columns.push({
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            width: '150px'
        })
    }
    let selectedRowKeys = ref([])
    // 列表选择配置
    const options = {
        alert: {
            show: false,
            clear: () => {
                selectedRowKeys = ref([])
            }
        },
        rowSelection: {
            onChange: (selectedRowKey, selectedRows) => {
                selectedRowKeys.value = selectedRowKey
            }
        }
    }
    const loadData = (parameter) => {
        const searchFormParam = JSON.parse(JSON.stringify(searchFormState))
        return releaseInfoApi.releaseInfoPage(Object.assign(parameter, searchFormParam)).then((data) => {
            return data
        })
    }
    // 删除
    const deleteReleaseInfo = (record) => {
        let params = [
            {
                id: record.id
            }
        ]
        releaseInfoApi.releaseInfoDelete(params).then(() => {
            table.value.refresh(true)
        })
    }
    // 批量删除
    const deleteBatchReleaseInfo = () => {
        if (selectedRowKeys.value.length < 1) {
            message.warning('请选择一条或多条数据')
            return false
        }
        const params = selectedRowKeys.value.map((m) => {
            return {
                id: m
            }
        })
        releaseInfoApi.releaseInfoDelete(params).then(() => {
            table.value.clearRefreshSelected()
        })
    }
    const timeTypeOptions = tool.dictList('mps_monitor_alarm_type')
    const releaseStatusOptions = tool.dictList('release_mps_release_status')
    const statusOptions = tool.dictList('status')
</script>
