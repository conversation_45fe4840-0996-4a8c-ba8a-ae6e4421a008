/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.release.modular.release.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.release.modular.release.entity.DpsReleaseInfo;
import vip.xiaonuo.release.modular.release.param.DpsReleaseInfoAddParam;
import vip.xiaonuo.release.modular.release.param.DpsReleaseInfoEditParam;
import vip.xiaonuo.release.modular.release.param.DpsReleaseInfoIdParam;
import vip.xiaonuo.release.modular.release.param.DpsReleaseInfoPageParam;
import vip.xiaonuo.release.modular.release.service.DpsReleaseInfoService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 灰度基本信息表(DPS)控制器
 *
 * <AUTHOR>
 * @date 2025/01/14 15:54
 */
@Api(tags = "灰度基本信息表(DPS)控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@RequestMapping("release/release/dpsReleaseInfo")
@Validated
@Slf4j
public class DpsReleaseInfoController {

    @Resource
    private DpsReleaseInfoService dpsReleaseInfoService;

    /**
     * 获取灰度基本信息表(DPS)分页
     *
     * <AUTHOR>
     * @date 2025/01/14 15:54
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取灰度基本信息表(DPS)分页")
    @GetMapping("page")
    public CommonResult<Page<DpsReleaseInfo>> page(DpsReleaseInfoPageParam dpsReleaseInfoPageParam) {
        return CommonResult.data(dpsReleaseInfoService.page(dpsReleaseInfoPageParam));
    }

    /**
     * 获取灰度基本信息表(DPS)列表
     *
     * <AUTHOR>
     * @date 2025/01/14 15:54
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取灰度基本信息表(DPS)列表")
    @GetMapping("list")
    public CommonResult<List<DpsReleaseInfo>> list(DpsReleaseInfoPageParam dpsReleaseInfoPageParam) {
        return CommonResult.data(dpsReleaseInfoService.list(dpsReleaseInfoPageParam));
    }

    /**
     * 添加灰度基本信息表(DPS)
     *
     * <AUTHOR>
     * @date 2025/01/14 15:54
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加灰度基本信息表(DPS)")
    @CommonLog("添加灰度基本信息表(DPS)")
    @PostMapping("add")
    public CommonResult<String> add(@RequestBody @Valid DpsReleaseInfoAddParam dpsReleaseInfoAddParam) {
        dpsReleaseInfoService.add(dpsReleaseInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑灰度基本信息表(DPS)
     *
     * <AUTHOR>
     * @date 2025/01/14 15:54
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑灰度基本信息表(DPS)")
    @CommonLog("编辑灰度基本信息表(DPS)")
    @PostMapping("edit")
    public CommonResult<String> edit(@RequestBody @Valid DpsReleaseInfoEditParam dpsReleaseInfoEditParam) {
        dpsReleaseInfoService.edit(dpsReleaseInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除灰度基本信息表(DPS)
     *
     * <AUTHOR>
     * @date 2025/01/14 15:54
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除灰度基本信息表(DPS)")
    @CommonLog("删除灰度基本信息表(DPS)")
    @PostMapping("delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<DpsReleaseInfoIdParam> dpsReleaseInfoIdParamList) {
        dpsReleaseInfoService.delete(dpsReleaseInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取灰度基本信息表(DPS)详情
     *
     * <AUTHOR>
     * @date 2025/01/14 15:54
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取灰度基本信息表(DPS)详情")
    @GetMapping("detail")
    public CommonResult<DpsReleaseInfo> detail(@Valid DpsReleaseInfoIdParam dpsReleaseInfoIdParam) {
        return CommonResult.data(dpsReleaseInfoService.detail(dpsReleaseInfoIdParam));
    }
}
