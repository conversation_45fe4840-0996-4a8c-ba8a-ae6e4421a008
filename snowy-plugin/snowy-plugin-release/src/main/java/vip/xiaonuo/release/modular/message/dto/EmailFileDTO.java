package vip.xiaonuo.release.modular.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 发送邮件附件
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/1 16:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "发送邮件附件")
public class EmailFileDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "附件内容，需要base64编码，utf-8编码", name = "base64file")
    private String base64file;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", name = "fileName")
    private String fileName;

    /**
     * 文本内容，未进行base64编码
     */
    @ApiModelProperty(value = "附件内容，未进行base64编码", name = "bodyNotBase64")
    private byte[] base64fileNotBase64;

    public String getBase64file() {
        if (StringUtils.isEmpty(base64file) && base64fileNotBase64 != null) {
            base64file = Base64.encodeBase64String(base64fileNotBase64);
        }
        return base64file;
    }
}
