/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.release.modular.release.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 灰度基本信息添加参数
 *
 * <AUTHOR>
 * @date 2024/11/08 17:27
 **/
@Getter
@Setter
public class ReleaseInfoAddParam {

    /**
     * 灰度名称
     */
    @ApiModelProperty(value = "灰度名称", required = true, position = 2)
    @NotBlank(message = "releaseName不能为空")
    private String releaseName;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", position = 2)
    @NotBlank(message = "应用ID不能为空")
    private String appId;

    /**
     * 云控模板ID
     */
    @ApiModelProperty(value = "云控模板ID", position = 2)
    @NotBlank(message = "云控模板ID不能为空")
    private String templateId;

    /**
     * 配置版本号
     */
    @ApiModelProperty(value = "配置版本号", position = 2)
    @NotBlank(message = "配置版本号不能为空")
    private String configVersion;

    /**
     * Lua任务名
     */
    @ApiModelProperty(value = "Lua任务名", position = 2)
    private String luaTask;

    /**
     * 使用的云控配置版本
     */
    @ApiModelProperty(value = "使用的云控配置版本", position = 2)
    @NotBlank(message = "使用的云控配置版本不能为空")
    private String useCloudControllerConfigVersion;

    /**
     * 时间类型(小时=hour,天=day)
     */
    @ApiModelProperty(value = "时间类型(小时=hour,天=day)", position = 3)
    private String timeType;

    /**
     * 接收方式(邮件=email,钉钉=dingding,机器人群消息=robot),JSONString
     */
    @ApiModelProperty(value = "接收方式(邮件=email,钉钉=dingding,机器人群消息=robot),JSONString", position = 4)
    private String notifyType;

    /**
     * 接收人ID,JSONString
     */
//    @ApiModelProperty(value = "接收人ID,JSONString", position = 5)
//    private String receiveUser;
    @ApiModelProperty(value = "接收人,List", required = true)
//    @NotEmpty(message = "接收人不能为空!")
    private List<String> receiveUsers;

    /**
     * 抄送人ID,JSONString
     */
//    @ApiModelProperty(value = "抄送人ID,JSONString", position = 6)
//    private String ccUser;
    @ApiModelProperty(value = "抄送人,List", name = "ccUsers")
    private List<String> ccUsers;

    /**
     * 灰度状态
     */
    @ApiModelProperty(value = "灰度状态", position = 7)
    private String releaseStatus;

    /**
     * 灰度内容
     */
    @ApiModelProperty(value = "灰度内容", required = true, position = 8)
    // @NotBlank(message = "releaseContent不能为空")
    private String releaseContent;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true, position = 9)
    // @NotBlank(message = "description不能为空")
    private String description;

    /**
     * 状态，删除=-1，无效=0，有效=1
     */
    @ApiModelProperty(value = "状态，删除=-1，无效=0，有效=1", position = 14)
    private String status;

}
