/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.release.modular.monitor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.release.core.constant.DataSourceConstant;
import vip.xiaonuo.release.modular.data.dto.DataCntDTO;
import vip.xiaonuo.release.modular.data.service.DpsService;
import vip.xiaonuo.release.modular.monitor.entity.ReleaseMonitorDpsHour;
import vip.xiaonuo.release.modular.monitor.mapper.ReleaseMonitorDpsHourMapper;
import vip.xiaonuo.release.modular.monitor.param.ReleaseMonitorDpsHourAddParam;
import vip.xiaonuo.release.modular.monitor.param.ReleaseMonitorDpsHourEditParam;
import vip.xiaonuo.release.modular.monitor.param.ReleaseMonitorDpsHourIdParam;
import vip.xiaonuo.release.modular.monitor.param.ReleaseMonitorDpsHourPageParam;
import vip.xiaonuo.release.modular.monitor.service.ReleaseMonitorDpsHourService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.release.modular.release.entity.DpsReleaseInfo;
import vip.xiaonuo.release.modular.release.entity.DpsReleaseStage;
import vip.xiaonuo.release.modular.release.service.DpsReleaseInfoService;
import vip.xiaonuo.release.modular.release.service.DpsReleaseStageService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 灰度发布监控数据(DPS)(小时)表Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/01/15 13:53
 **/
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_RELEASE_MONITOR)
@Slf4j
public class ReleaseMonitorDpsHourServiceImpl extends ServiceImpl<ReleaseMonitorDpsHourMapper, ReleaseMonitorDpsHour> implements ReleaseMonitorDpsHourService {
    @Autowired
    private DpsService dpsService;

    @Autowired
    private DpsReleaseInfoService dpsReleaseInfoService;

    @Autowired
    private DpsReleaseStageService dpsReleaseStageService;

    @Override
    public Page<ReleaseMonitorDpsHour> page(ReleaseMonitorDpsHourPageParam releaseMonitorDpsHourPageParam) {
        QueryWrapper<ReleaseMonitorDpsHour> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getReleaseId())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getReleaseId, releaseMonitorDpsHourPageParam.getReleaseId());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getStageId())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStageId, releaseMonitorDpsHourPageParam.getStageId());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getTimeType())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getTimeType, releaseMonitorDpsHourPageParam.getTimeType());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getDataType())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getDataType, releaseMonitorDpsHourPageParam.getDataType());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getStatus())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStatus, releaseMonitorDpsHourPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(releaseMonitorDpsHourPageParam.getSortField(), releaseMonitorDpsHourPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(releaseMonitorDpsHourPageParam.getSortOrder());
            queryWrapper.orderBy(true, releaseMonitorDpsHourPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(releaseMonitorDpsHourPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(ReleaseMonitorDpsHour::getCreateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<ReleaseMonitorDpsHour> list(ReleaseMonitorDpsHourPageParam releaseMonitorDpsHourPageParam) {
        QueryWrapper<ReleaseMonitorDpsHour> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getReleaseId())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getReleaseId, releaseMonitorDpsHourPageParam.getReleaseId());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getStageId())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStageId, releaseMonitorDpsHourPageParam.getStageId());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getTimeType())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getTimeType, releaseMonitorDpsHourPageParam.getTimeType());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getDataType())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getDataType, releaseMonitorDpsHourPageParam.getDataType());
        }
        if (ObjectUtil.isNotEmpty(releaseMonitorDpsHourPageParam.getStatus())) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStatus, releaseMonitorDpsHourPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(releaseMonitorDpsHourPageParam.getSortField(), releaseMonitorDpsHourPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(releaseMonitorDpsHourPageParam.getSortOrder());
            queryWrapper.orderBy(true, releaseMonitorDpsHourPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(releaseMonitorDpsHourPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(ReleaseMonitorDpsHour::getCreateTime);
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(ReleaseMonitorDpsHourAddParam releaseMonitorDpsHourAddParam) {
        ReleaseMonitorDpsHour releaseMonitorDpsHour = BeanUtil.toBean(releaseMonitorDpsHourAddParam, ReleaseMonitorDpsHour.class);
        this.save(releaseMonitorDpsHour);
    }

    @Override
    public void edit(ReleaseMonitorDpsHourEditParam releaseMonitorDpsHourEditParam) {
        ReleaseMonitorDpsHour releaseMonitorDpsHour = this.queryEntity(releaseMonitorDpsHourEditParam.getId());
        BeanUtil.copyProperties(releaseMonitorDpsHourEditParam, releaseMonitorDpsHour);
        this.updateById(releaseMonitorDpsHour);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<ReleaseMonitorDpsHourIdParam> releaseMonitorDpsHourIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(releaseMonitorDpsHourIdParamList, ReleaseMonitorDpsHourIdParam::getId));
    }

    @Override
    public ReleaseMonitorDpsHour detail(ReleaseMonitorDpsHourIdParam releaseMonitorDpsHourIdParam) {
        return this.queryEntity(releaseMonitorDpsHourIdParam.getId());
    }

    @Override
    public ReleaseMonitorDpsHour queryEntity(String id) {
        ReleaseMonitorDpsHour releaseMonitorDpsHour = this.getById(id);
        if (ObjectUtil.isEmpty(releaseMonitorDpsHour)) {
            throw new CommonException("灰度发布监控数据(DPS)(小时)表不存在，id值为：{}", id);
        }
        return releaseMonitorDpsHour;
    }

    @Override
    public Boolean importMonitorData(String dayHour, String dayOrHour) {
        Boolean importMonitorDataResult = Boolean.TRUE;

        // dayOrHour =day按天统计，=hour 按小时统计，默认小时
        dayOrHour = StringUtils.defaultIfBlank(dayOrHour, "day");
        Date date = null;
        String startTime = null;
        String endTime = null;
        if (StringUtils.equalsIgnoreCase(dayOrHour, "day")) {
            date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(dayHour)).toJdkDate();
            startTime = DateUtil.beginOfDay(date).toString(DatePattern.NORM_DATETIME_PATTERN);
            endTime = DateUtil.endOfDay(date).toString(DatePattern.NORM_DATETIME_PATTERN);
        } else if (StringUtils.equalsIgnoreCase(dayOrHour, "hour")) {
            date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.beginOfHour(DateUtil.parse(dayHour)).toJdkDate();
            startTime = DateUtil.beginOfHour(date).toString(DatePattern.NORM_DATETIME_PATTERN);
            endTime = DateUtil.endOfHour(date).toString(DatePattern.NORM_DATETIME_PATTERN);
        } else {
            log.warn("不支持dayOrHour=[{}]统计类型!", dayOrHour);
            return importMonitorDataResult;
        }

        String day = DateUtil.format(date, "yyyy-MM-dd");
        Integer hour = DateUtil.hour(date, Boolean.TRUE);
        // 时间类型(小时=hour,天=day)
        String timeType = dayOrHour;
        // 数据类型
        String dataType = "total";

        // 获取进行中的灰度梯度
        // 是否自动发布，手动发布=0，自动发布=1
        // 梯度状态(待发布=0，灰度中=1，已停止=2，已完成=3，发布失败=4)
        // 状态，删除=-1，无效=0，有效=1
        List<DpsReleaseStage> releaseStageList = dpsReleaseStageService.listOngoingReleaseStage(endTime, null, "1", "1");
        if (CollectionUtils.isNotEmpty(releaseStageList)) {
            for (DpsReleaseStage releaseStage : releaseStageList) {
                // 灰度发布基本信息ID
                String releaseId = releaseStage.getReleaseId();
                // 灰度梯度ID
                String stageId = releaseStage.getStageId();
                DpsReleaseInfo releaseInfo = dpsReleaseInfoService.getById(releaseId);
                if (releaseInfo == null) {
                    log.warn("根据梯度关联的发布基本信息ID无法获取到发布基本信息，将不执行数据导入！releaseId=[{}],releaseStage=[{}]", releaseId, JSONObject.toJSONString(releaseStage));
                    continue;
                }
                Long appId = Long.valueOf(releaseInfo.getAppId());
                String sdkVersion = releaseInfo.getSdkVersion();
                String promoteIds = releaseStage.getPromoteId();
                List<Long> promoteIdList = new ArrayList<>();
                // 渠道号为*表示全量
                if (StringUtils.isNotBlank(StringUtils.deleteWhitespace(promoteIds)) && !StringUtils.contains(promoteIds, "*")) {
                    List<String> promoteIdStrList = Arrays.asList(StringUtils.splitPreserveAllTokens(StringUtils.deleteWhitespace(promoteIds), ","));
                    if (CollectionUtils.isNotEmpty(promoteIdStrList)) {
                        for (String promoteIdStr : promoteIdStrList) {
                            promoteIdList.add(Long.valueOf(promoteIdStr));
                        }
                    }
                }

//                if (StringUtils.isBlank(sdkVersion) && CollectionUtils.isEmpty(promoteIdList)) {
//                    log.warn("SDK版本号和渠道号列表均为空，将不执行数据导入!");
//                    continue;
//                }

                // 待保存的数据
                List<ReleaseMonitorDpsHour> releaseMonitorDpsHourList = new ArrayList<>();

                try {
                    // DPS用户数
                    // DPS用户数数据(新配置)
                    // TODO 注意：DPS暂无法统计新配置用户数，使用灰度渠道用户数代替(默认只要开始灰度，对应渠道用户100%获取到最新配置)
                    List<DataCntDTO> dataCntDTODpsNewConfigUserList = dpsService.listDAU(startTime, endTime, appId, sdkVersion, promoteIdList);
                    // DPS用户数数据(灰度渠道)
                    List<DataCntDTO> dataCntDTODpsPromoteidUserList = dpsService.listDAU(startTime, endTime, appId, sdkVersion, promoteIdList);

                    // 错误数据
                    // 错误数据(大厅总)
                    List<DataCntDTO> dataCntDTOAllTotalErrorList = dpsService.listErrorDataCnt(startTime, endTime, appId, sdkVersion, null);
                    // 错误数据(DPS总)
                    List<DataCntDTO> dataCntDTODpsTotalErrorList = dpsService.listErrorDataCnt(startTime, endTime, appId, sdkVersion, null);
                    // 错误数据(DPS灰度渠道)
                    List<DataCntDTO> dataCntDTODpsPromoteidErrorList = dpsService.listErrorDataCnt(startTime, endTime, appId, sdkVersion, promoteIdList);

                    // DPS用户数(新配置)
                    if (CollectionUtils.isNotEmpty(dataCntDTODpsNewConfigUserList)) {
                        for (DataCntDTO dataCntDTO : dataCntDTODpsNewConfigUserList) {
                            ReleaseMonitorDpsHour releaseMonitorDpsHour = new ReleaseMonitorDpsHour();
                            // DPS用户数(新配置)
                            releaseMonitorDpsHour.setCntUserDpsNewConfig(dataCntDTO.getUserIdCnt());
                            releaseMonitorDpsHour.setDay(dataCntDTO.getDay());
                            // TODO 小时
                            releaseMonitorDpsHour.setHour(hour);
                            CollectionUtils.addIgnoreNull(releaseMonitorDpsHourList, releaseMonitorDpsHour);
                        }
                    }

                    // DPS用户数数据(灰度渠道)
                    if (CollectionUtils.isNotEmpty(dataCntDTODpsPromoteidUserList)) {
                        for (DataCntDTO dataCntDTO : dataCntDTODpsPromoteidUserList) {
                            ReleaseMonitorDpsHour releaseMonitorDpsHour = new ReleaseMonitorDpsHour();
                            // DPS用户数(灰度渠道)
                            releaseMonitorDpsHour.setCntUserDpsPromoteid(dataCntDTO.getUserIdCnt());
                            releaseMonitorDpsHour.setDay(dataCntDTO.getDay());
                            // TODO 小时
                            releaseMonitorDpsHour.setHour(hour);

                            CollectionUtils.addIgnoreNull(releaseMonitorDpsHourList, releaseMonitorDpsHour);
                        }
                    }

                    // 错误数据(大厅总)
                    if (CollectionUtils.isNotEmpty(dataCntDTOAllTotalErrorList)) {
                        for (DataCntDTO dataCntDTO : dataCntDTOAllTotalErrorList) {
                            ReleaseMonitorDpsHour releaseMonitorDpsHour = new ReleaseMonitorDpsHour();
                            // 错误发生次数(大厅总)
                            releaseMonitorDpsHour.setCntDataErrorAllTotal(dataCntDTO.getDataCnt());
                            // 错误用户数(大厅总)
                            releaseMonitorDpsHour.setCntUserErrorAllTotal(dataCntDTO.getUserIdCnt());
                            releaseMonitorDpsHour.setDay(dataCntDTO.getDay());
                            // TODO 小时
                            releaseMonitorDpsHour.setHour(hour);

                            CollectionUtils.addIgnoreNull(releaseMonitorDpsHourList, releaseMonitorDpsHour);
                        }
                    }

                    // 错误数据(DPS总)
                    if (CollectionUtils.isNotEmpty(dataCntDTODpsTotalErrorList)) {
                        for (DataCntDTO dataCntDTO : dataCntDTODpsTotalErrorList) {
                            ReleaseMonitorDpsHour releaseMonitorDpsHour = new ReleaseMonitorDpsHour();
                            // 错误发生次数(DPS总)
                            releaseMonitorDpsHour.setCntDataErrorDpsTotal(dataCntDTO.getDataCnt());
                            // 错误用户数(DPS总)
                            releaseMonitorDpsHour.setCntUserErrorDpsTotal(dataCntDTO.getUserIdCnt());
                            releaseMonitorDpsHour.setDay(dataCntDTO.getDay());
                            // TODO 小时
                            releaseMonitorDpsHour.setHour(hour);

                            CollectionUtils.addIgnoreNull(releaseMonitorDpsHourList, releaseMonitorDpsHour);
                        }
                    }

                    // 错误数据(DPS灰度渠道)
                    if (CollectionUtils.isNotEmpty(dataCntDTODpsPromoteidErrorList)) {
                        for (DataCntDTO dataCntDTO : dataCntDTODpsPromoteidErrorList) {
                            ReleaseMonitorDpsHour releaseMonitorDpsHour = new ReleaseMonitorDpsHour();
                            // 错误发生次数(DPS灰度渠道)
                            releaseMonitorDpsHour.setCntDataErrorDpsPromoteid(dataCntDTO.getDataCnt());
                            // 错误用户数(DPS灰度渠道)
                            releaseMonitorDpsHour.setCntUserErrorDpsPromoteid(dataCntDTO.getUserIdCnt());
                            releaseMonitorDpsHour.setDay(dataCntDTO.getDay());
                            // TODO 小时
                            releaseMonitorDpsHour.setHour(hour);

                            CollectionUtils.addIgnoreNull(releaseMonitorDpsHourList, releaseMonitorDpsHour);
                        }
                    }
                    // 处理公共字段
                    if (CollectionUtils.isNotEmpty(releaseMonitorDpsHourList)) {
                        for (ReleaseMonitorDpsHour releaseMonitorDpsHour : releaseMonitorDpsHourList) {
                            releaseMonitorDpsHour.setAppId(appId);
                            releaseMonitorDpsHour.setSdkVersion(sdkVersion);
                            if (CollectionUtils.isNotEmpty(promoteIdList)) {
                                releaseMonitorDpsHour.setPromoteId(JSONObject.toJSONString(promoteIdList));
                            }
                            releaseMonitorDpsHour.setReleaseId(releaseId);
                            releaseMonitorDpsHour.setStageId(stageId);
                            releaseMonitorDpsHour.setTimeType(timeType);
                            releaseMonitorDpsHour.setDataType(dataType);
                            // id,由时间类型、数据类型、日期、小时、灰度发布ID、灰度梯度ID拼接而来
                            String id = this.generateMonitorHourId(releaseMonitorDpsHour);
                            releaseMonitorDpsHour.setId(id);
                            // 日期(小时)的datetime类型
                            releaseMonitorDpsHour.setDt(DateUtil.parse(releaseMonitorDpsHour.getDay()).offsetNew(DateField.HOUR_OF_DAY, releaseMonitorDpsHour.getHour()).toJdkDate());
                            // 状态，-1删除，0无效，1有效
                            releaseMonitorDpsHour.setStatus("1");
                        }
                    }
                } catch (Exception e) {
                    log.warn("执行导入监控数据失败，导入数据日期=[{}]！", startTime, e);
                    importMonitorDataResult = Boolean.FALSE;
                }

                // 首先删除所有该时间段数据以便重新导入
                QueryWrapper<ReleaseMonitorDpsHour> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getDay, day);
                queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getHour, hour);
                queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getDataType, dataType);
                queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getTimeType, timeType);
                queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getReleaseId, releaseId);
                queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStageId, stageId);
                this.remove(queryWrapper);

                // 保存数据
                this.saveOrUpdateBatch(releaseMonitorDpsHourList);
                // 计算率
                List<ReleaseMonitorDpsHour> releaseMonitorDpsHourRateList = this.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(releaseMonitorDpsHourRateList)) {
                    for (ReleaseMonitorDpsHour releaseMonitorDpsHour : releaseMonitorDpsHourRateList) {

                        // 总错误次数率(DPS总错误发生次数/大厅总错误发生次数)
                        BigDecimal rateDataTotal = BigDecimal.ZERO;
                        // 总错误用户率(DPS总错误用户数/大厅总错误用户数)
                        BigDecimal rateUserTotal = BigDecimal.ZERO;
                        // 灰度渠道错误用户率(DPS灰度渠道错误用户数/DPS用户数(灰度渠道))
                        BigDecimal rateUserPromoteid = BigDecimal.ZERO;

                        // 总错误次数率(DPS总错误发生次数/大厅总错误发生次数)
                        if (!Long.valueOf(0).equals(releaseMonitorDpsHour.getCntDataErrorAllTotal())) {
                            rateDataTotal = BigDecimal.valueOf(releaseMonitorDpsHour.getCntDataErrorDpsTotal()).divide(BigDecimal.valueOf(releaseMonitorDpsHour.getCntDataErrorAllTotal()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100.00));
                        }
                        // 总错误用户率(DPS总错误用户数/大厅总错误用户数)
                        if (!Long.valueOf(0).equals(releaseMonitorDpsHour.getCntUserErrorAllTotal())) {
                            rateUserTotal = BigDecimal.valueOf(releaseMonitorDpsHour.getCntUserErrorDpsTotal()).divide(BigDecimal.valueOf(releaseMonitorDpsHour.getCntUserErrorAllTotal()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100.00));
                        }
                        // 灰度渠道错误用户率(DPS灰度渠道错误用户数/DPS用户数(灰度渠道))
                        if (!Long.valueOf(0).equals(releaseMonitorDpsHour.getCntUserDpsPromoteid())) {
                            rateUserPromoteid = BigDecimal.valueOf(releaseMonitorDpsHour.getCntUserErrorDpsPromoteid()).divide(BigDecimal.valueOf(releaseMonitorDpsHour.getCntUserDpsPromoteid()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100.00));
                        }
                        releaseMonitorDpsHour.setRateDataTotal(rateDataTotal);
                        releaseMonitorDpsHour.setRateUserTotal(rateUserTotal);
                        releaseMonitorDpsHour.setRateUserPromoteid(rateUserPromoteid);
                    }
                    this.saveOrUpdateBatch(releaseMonitorDpsHourRateList);
                }
            }
        }

        // TODO 补充空白数据(只补充目前应用状态为有效应用的汇总数据)

        return importMonitorDataResult;
    }

    @Override
    public List<ReleaseMonitorDpsHour> listHistoricalMonitoDayData(String releaseId, String stageId, String startDay, String endDay) {
        if (StringUtils.isNotBlank(startDay)) {
            startDay = DateUtil.format(DateUtil.parse(startDay), DatePattern.NORM_DATE_PATTERN);
        }
        if (StringUtils.isNotBlank(endDay)) {
            endDay = DateUtil.format(DateUtil.parse(endDay), DatePattern.NORM_DATE_PATTERN);
        }

        String timeType = "day";
        String dataType = "total";
        String status = "1";
        QueryWrapper<ReleaseMonitorDpsHour> queryWrapper = new QueryWrapper<>();

        if (ObjectUtil.isNotEmpty(releaseId)) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getReleaseId, releaseId);
        }
        if (ObjectUtil.isNotEmpty(stageId)) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStageId, stageId);
        }
        if (ObjectUtil.isNotEmpty(timeType)) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getTimeType, timeType);
        }
        if (ObjectUtil.isNotEmpty(dataType)) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getDataType, dataType);
        }
        if (ObjectUtil.isNotEmpty(status)) {
            queryWrapper.lambda().eq(ReleaseMonitorDpsHour::getStatus, status);
        }
        queryWrapper.lambda().ge(StringUtils.isNotBlank(startDay), ReleaseMonitorDpsHour::getDay, startDay);
        queryWrapper.lambda().le(StringUtils.isNotBlank(endDay), ReleaseMonitorDpsHour::getDay, endDay);
        queryWrapper.lambda().orderByDesc(ReleaseMonitorDpsHour::getDt).orderByDesc(ReleaseMonitorDpsHour::getCreateTime);

        return this.list(queryWrapper);
    }

    private String generateMonitorHourId(ReleaseMonitorDpsHour releaseMonitorDpsHour) {
        // id,由时间类型、数据类型、日期、小时、灰度发布ID、灰度梯度ID拼接而来
        String id = String.format("%s_%s_%s%s_%s_%s", releaseMonitorDpsHour.getTimeType(), releaseMonitorDpsHour.getDataType(), StringUtils.replace(releaseMonitorDpsHour.getDay(), "-", ""), String.format("%02d", releaseMonitorDpsHour.getHour()), releaseMonitorDpsHour.getReleaseId(), releaseMonitorDpsHour.getStageId());
        return id;
    }
}
