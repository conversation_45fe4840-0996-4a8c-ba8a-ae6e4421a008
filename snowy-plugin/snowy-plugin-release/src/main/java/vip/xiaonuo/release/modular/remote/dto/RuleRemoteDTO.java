package vip.xiaonuo.release.modular.remote.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/29 11:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "规则远程传输对象")
public class RuleRemoteDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty(value = "id", name = "id", required = true)
    @JsonProperty("id")
    private Integer id;

    @ApiModelProperty(value = "createdBy", name = "createdBy", required = true)
    @JsonProperty("created_by")
    private String createdBy;

    @ApiModelProperty(value = "modifiedBy", name = "modifiedBy", required = true)
    @JsonProperty("modified_by")
    private String modifiedBy;

    @ApiModelProperty(value = "createdOn", name = "createdOn", required = true)
    @JsonProperty("created_on")
    private Long createdOn;

    @ApiModelProperty(value = "modifiedOn", name = "modifiedOn", required = true)
    @JsonProperty("modified_on")
    private Long modifiedOn;

    @ApiModelProperty(value = "deletedOn", name = "deletedOn", required = true)
    @JsonProperty("deleted_on")
    private Long deletedOn;

    @ApiModelProperty(value = "isDel", name = "isDel", required = true)
    @JsonProperty("is_del")
    private Integer isDel;

    @ApiModelProperty(value = "configType", name = "configType", required = true)
    @JsonProperty("config_type")
    private Integer configType;

    @ApiModelProperty(value = "configVersion", name = "configVersion", required = true)
    @JsonProperty("config_version")
    private Integer configVersion;

    @ApiModelProperty(value = "模板ID", name = "templateId", required = true)
    @JsonProperty("template_id")
    private Long templateId;

    @ApiModelProperty(value = "patterns", name = "patterns", required = true)
    @JsonProperty("patterns")
    private String patterns;

    @ApiModelProperty(value = "ruleName", name = "ruleName", required = true)
    @JsonProperty("rule_name")
    private String ruleName;

    @ApiModelProperty(value = "状态(0或1)", name = "state", required = true)
    @JsonProperty("state")
    private Integer state;

    @ApiModelProperty(value = "createdOnZh", name = "createdOnZh", required = false)
    private Date createdOnZh;

    @ApiModelProperty(value = "modifiedOnZh", name = "modifiedOnZh", required = false)
    private Date modifiedOnZh;

    @ApiModelProperty(value = "deletedOnZh", name = "deletedOnZh", required = false)
    private Date deletedOnZh;

    public Date getCreatedOnZh() {
        if (createdOn != null && !Long.valueOf(0).equals(createdOn)) {
            createdOnZh = new Date(createdOn * 1000L);
        }
        return createdOnZh;
    }

    public Date getModifiedOnZh() {
        if (modifiedOn != null && !Long.valueOf(0).equals(modifiedOn)) {
            modifiedOnZh = new Date(modifiedOn * 1000L);
        }
        return modifiedOnZh;
    }

    public Date getDeletedOnZh() {
        if (deletedOn != null && !Long.valueOf(0).equals(deletedOn)) {
            deletedOnZh = new Date(deletedOn * 1000L);
        }
        return deletedOnZh;
    }
}
