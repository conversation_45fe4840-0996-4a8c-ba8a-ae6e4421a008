package vip.xiaonuo.release.modular.query.constant;

import java.io.Serializable;

/**
 * ES数据查询相关常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/2/1 14:05
 */
public class EsQueryConstant implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * ES数据查询-崩溃数据(小时或天)查询模板
     */
    public static final String ES_RELEASE_DATA_AB_TEMPLATE = "release/es/es_release_data_ab_template.json.ftl";
}
