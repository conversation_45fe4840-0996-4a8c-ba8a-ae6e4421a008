package vip.xiaonuo.release.core.enums;

import lombok.Getter;

/**
 * 系统内置的不可删除的标识枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/5 14:59
 */
@Getter
public enum ReleaseBuildInEnum {
    /**
     * 超管用户账号
     */
    BUILD_IN_USER_ACCOUNT("superAdmin", "超管");

    private final String value;

    private final String name;

    ReleaseBuildInEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
