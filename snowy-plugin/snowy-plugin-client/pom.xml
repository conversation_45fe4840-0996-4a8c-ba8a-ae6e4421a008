<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>vip.xiaonuo</groupId>
        <artifactId>snowy-plugin</artifactId>
        <version>2.0.0</version>
    </parent>

    <artifactId>snowy-plugin-client</artifactId>
    <packaging>jar</packaging>
    <description>C端功能插件</description>

    <dependencies>
        <!-- 每个插件都要引入自己的对外接口 -->
        <dependency>
            <groupId>vip.xiaonuo</groupId>
            <artifactId>snowy-plugin-client-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- 引入登录鉴权接口，用于实现其C端登录所需逻辑 -->
        <dependency>
            <groupId>vip.xiaonuo</groupId>
            <artifactId>snowy-plugin-auth-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <!-- 引入开发工具接口，用于配置信息 -->
        <dependency>
            <groupId>vip.xiaonuo</groupId>
            <artifactId>snowy-plugin-dev-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
    </dependencies>
</project>