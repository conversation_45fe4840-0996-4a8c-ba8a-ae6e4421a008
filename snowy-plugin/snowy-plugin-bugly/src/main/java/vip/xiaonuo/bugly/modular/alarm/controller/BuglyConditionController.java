/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyCondition;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyConditionAddParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyConditionEditParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyConditionIdParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyConditionPageParam;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyConditionService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 告警条件控制器
 *
 * <AUTHOR>
 * @date 2024/01/20 14:52
 */
@Api(tags = "告警条件控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@RequestMapping("bugly/alarm/condition")
@Validated
public class BuglyConditionController {

    @Resource
    private BuglyConditionService buglyConditionService;

    /**
     * 获取告警条件分页
     *
     * <AUTHOR>
     * @date 2024/01/20 14:52
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取告警条件分页")
    @GetMapping("page")
    public CommonResult<Page<BuglyCondition>> page(BuglyConditionPageParam buglyConditionPageParam) {
        return CommonResult.data(buglyConditionService.page(buglyConditionPageParam));
    }

    /**
     * 获取告警条件列表
     *
     * <AUTHOR>
     * @date 2024/01/20 14:52
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取告警条件列表")
    @GetMapping("list")
    public CommonResult<List<BuglyCondition>> list(BuglyConditionPageParam buglyConditionPageParam) {
        return CommonResult.data(buglyConditionService.list(buglyConditionPageParam));
    }

    /**
     * 添加告警条件
     *
     * <AUTHOR>
     * @date 2024/01/20 14:52
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加告警条件")
    @CommonLog("添加告警条件")
    @PostMapping("add")
    public CommonResult<String> add(@RequestBody @Valid BuglyConditionAddParam buglyConditionAddParam) {
        buglyConditionService.add(buglyConditionAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑告警条件
     *
     * <AUTHOR>
     * @date 2024/01/20 14:52
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑告警条件")
    @CommonLog("编辑告警条件")
    @PostMapping("edit")
    public CommonResult<String> edit(@RequestBody @Valid BuglyConditionEditParam buglyConditionEditParam) {
        buglyConditionService.edit(buglyConditionEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除告警条件
     *
     * <AUTHOR>
     * @date 2024/01/20 14:52
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除告警条件")
    @CommonLog("删除告警条件")
    @PostMapping("delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<BuglyConditionIdParam> buglyConditionIdParamList) {
        buglyConditionService.delete(buglyConditionIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取告警条件详情
     *
     * <AUTHOR>
     * @date 2024/01/20 14:52
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取告警条件详情")
    @GetMapping("detail")
    public CommonResult<BuglyCondition> detail(@Valid BuglyConditionIdParam buglyConditionIdParam) {
        return CommonResult.data(buglyConditionService.detail(buglyConditionIdParam));
    }
}
