/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 告警基本信息添加参数
 *
 * <AUTHOR>
 * @date 2024/01/20 14:49
 **/
@Getter
@Setter
public class BuglyAlarmAddParam {

    /**
     * 告警名称
     */
    @ApiModelProperty(value = "告警名称", required = true, position = 2)
    @NotBlank(message = "alarmName不能为空")
    private String alarmName;

    /**
     * 统计周期(最近1小时=hour,最近一天=day,最近1小时数据校验=hour_ab,最近一天数据校验=day_ab)
     */
    @ApiModelProperty(value = "统计周期(最近1小时=hour,最近一天=day,最近1小时数据校验=hour_ab,最近一天数据校验=day_ab)", required = true, position = 3)
    @NotBlank(message = "alarmType不能为空")
    private String alarmType;

    /**
     * 接收方式(邮件=email,钉钉=dingding,机器人群消息=robot),JSONString
     */
//    @ApiModelProperty(value = "接收方式(邮件=email,钉钉=dingding,机器人群消息=robot),JSONString", required = true, position = 4)
//    @NotBlank(message = "notifyType不能为空")
//    private String notifyType;
    /**
     * 接收方式,List
     */
    @ApiModelProperty(value = "接收方式,List", name = "notifyTypes", required = true)
    @NotEmpty(message = "接收方式不能为空!")
    private List<String> notifyTypes;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", required = true, position = 5)
    @NotBlank(message = "appId不能为空")
    private String appId;

    /**
     * APP版本号(该字段非空则告警作用于特定版本)
     */
    @ApiModelProperty(value = "APP版本号(该字段非空则告警作用于特定版本)", position = 6)
    private String appVersion;

    /**
     * APP渠道号(该字段非空则告警作用于特定渠道)
     */
    @ApiModelProperty(value = "APP渠道号(该字段非空则告警作用于特定渠道)", position = 7)
    private String appChannel;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", position = 8)
    private String description;

    /**
     * 状态，删除=-1，无效=0，有效=1
     */
    @ApiModelProperty(value = "状态，删除=-1，无效=0，有效=1", required = true, position = 13)
//    @NotBlank(message = "status不能为空")
    private String status;

    /**
     * 告警基本信息与告警条件关联信息
     */
    @ApiModelProperty(value = "告警基本信息与告警条件关联信息", name = "alarmConditionLinkList")
    @Valid
    private List<BuglyAlarmConditionLinkAddParam> alarmConditionLinkList;

}
