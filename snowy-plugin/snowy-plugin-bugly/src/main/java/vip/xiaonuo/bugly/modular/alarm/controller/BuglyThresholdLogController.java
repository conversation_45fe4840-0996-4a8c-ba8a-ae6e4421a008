/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyThresholdLog;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyThresholdLogAddParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyThresholdLogEditParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyThresholdLogIdParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyThresholdLogPageParam;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyThresholdLogService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 监控阈值及触发结果日志记录表控制器
 *
 * <AUTHOR>
 * @date 2024/01/20 14:58
 */
@Api(tags = "监控阈值及触发结果日志记录表控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@RequestMapping("bugly/alarm/thresholdLog")
@Validated
@Slf4j
public class BuglyThresholdLogController {

    @Resource
    private BuglyThresholdLogService buglyThresholdLogService;

    /**
     * 获取监控阈值及触发结果日志记录表分页
     *
     * <AUTHOR>
     * @date 2024/01/20 14:58
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取监控阈值及触发结果日志记录表分页")
    @GetMapping("page")
    public CommonResult<Page<BuglyThresholdLog>> page(BuglyThresholdLogPageParam buglyThresholdLogPageParam) {
        return CommonResult.data(buglyThresholdLogService.page(buglyThresholdLogPageParam));
    }

    /**
     * 添加监控阈值及触发结果日志记录表
     *
     * <AUTHOR>
     * @date 2024/01/20 14:58
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加监控阈值及触发结果日志记录表")
    @CommonLog("添加监控阈值及触发结果日志记录表")
    @PostMapping("add")
    public CommonResult<String> add(@RequestBody @Valid BuglyThresholdLogAddParam buglyThresholdLogAddParam) {
        buglyThresholdLogService.add(buglyThresholdLogAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑监控阈值及触发结果日志记录表
     *
     * <AUTHOR>
     * @date 2024/01/20 14:58
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑监控阈值及触发结果日志记录表")
    @CommonLog("编辑监控阈值及触发结果日志记录表")
    @PostMapping("edit")
    public CommonResult<String> edit(@RequestBody @Valid BuglyThresholdLogEditParam buglyThresholdLogEditParam) {
        buglyThresholdLogService.edit(buglyThresholdLogEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除监控阈值及触发结果日志记录表
     *
     * <AUTHOR>
     * @date 2024/01/20 14:58
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除监控阈值及触发结果日志记录表")
    @CommonLog("删除监控阈值及触发结果日志记录表")
    @PostMapping("delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<BuglyThresholdLogIdParam> buglyThresholdLogIdParamList) {
        buglyThresholdLogService.delete(buglyThresholdLogIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取监控阈值及触发结果日志记录表详情
     *
     * <AUTHOR>
     * @date 2024/01/20 14:58
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取监控阈值及触发结果日志记录表详情")
    @GetMapping("detail")
    public CommonResult<BuglyThresholdLog> detail(@Valid BuglyThresholdLogIdParam buglyThresholdLogIdParam) {
        return CommonResult.data(buglyThresholdLogService.detail(buglyThresholdLogIdParam));
    }

    /**
     * 根据时间监控阈值及触发结果日志记录(天或小时)
     *
     * @param dayHour   时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour 告警周期(最近1小时=hour,最近一天=day,最近1小时数据校验=hour_ab,最近一天数据校验=day_ab)，默认小时
     * @return 执行结果
     */
    @ApiOperation(value = "根据时间监控阈值及触发结果日志记录(天或小时)", notes = "根据时间监控阈值及触发结果日志记录(天或小时)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dayHour", value = "时间，若告警周期为小时则默认当前时间减1小时开始时间，若告警周期为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "告警周期(最近1小时=hour,最近一天=day,最近1小时数据校验=hour_ab,最近一天数据校验=day_ab)，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("importMonitorAlarmThresholdLog")
    public CommonResult<Boolean> importMonitorAlarmThresholdLog(
            @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {
        dayOrHour = StringUtils.defaultIfBlank(dayOrHour, "hour");
        if (StringUtils.equalsIgnoreCase(dayOrHour, "hour")) {
            buglyThresholdLogService.importMonitorHourAlarmThresholdLog(dayHour);
        }
        return CommonResult.data(Boolean.TRUE);
    }

    @ApiOperation(value = "根据时间范围导入监控阈值及触发结果日志记录(天或小时)", notes = "根据时间范围导入监控阈值及触发结果日志记录(天或小时)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间(包含)，格式yyyy-MM-dd HH:mm:ss,默认当前时间减1天开始时间", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "结束时间(包含)，格式yyyy-MM-dd HH:mm:ss,默认当前时间减1天结束时间", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dayOrHour", value = "=告警周期(最近1小时=hour,最近一天=day,最近1小时数据校验=hour_ab,最近一天数据校验=day_ab)，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("importMonitorAlarmThresholdLogList")
    public CommonResult<Boolean> importMonitorAlarmThresholdLogList(
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {

        dayOrHour = StringUtils.defaultIfBlank(dayOrHour, "hour");

        // 获取时间区间日期
        List<DateTime> dateTimeList = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(dayOrHour, "day")) {
            Date start = StringUtils.isBlank(startTime) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(startTime)).toJdkDate();
            Date end = StringUtils.isBlank(endTime) ? DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.endOfDay(DateUtil.parse(endTime)).toJdkDate();
            dateTimeList = DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);
        } else if (StringUtils.equalsIgnoreCase(dayOrHour, "hour")) {
            Date start = StringUtils.isBlank(startTime) ? DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.beginOfHour(DateUtil.parse(startTime)).toJdkDate();
            Date end = StringUtils.isBlank(endTime) ? DateUtil.endOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.endOfHour(DateUtil.parse(endTime)).toJdkDate();
            dateTimeList = DateUtil.rangeToList(start, end, DateField.HOUR_OF_DAY);
            for (DateTime dateTime : dateTimeList) {
                String dayHour = DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN);
                log.info("批量导入监控阈值及触发结果日志记录dayOrHour=[{}],dayHour=[{}]", dayOrHour, dayHour);
                buglyThresholdLogService.importMonitorHourAlarmThresholdLog(dayHour);
            }
        }
        return CommonResult.data(Boolean.TRUE);

    }
}
