package vip.xiaonuo.bugly.modular.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyAlarmService;
import vip.xiaonuo.bugly.modular.message.service.MessageService;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * Bugly监控日报定时任务(天)
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/5/24 10:21
 */
@Component
@Slf4j
public class MonitorBuglyDayReportTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private MessageService messageService;

    @Resource
    private BuglyAlarmService alarmService;

    @Override
    public void action() {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行日报(天)定时任务......");
            // 发送日报(天)
            alarmService.sendMonitorDayReportAlarm(null);
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            log.info(String.format("执行日报(天)定时任务成功,耗时=[%s]ms。", duration.toMillis()));
        } catch (Exception e) {
            messageService.sendXingeAdministratorMessage("执行日报(天)定时任务失败", null);
            log.error("执行日报(天)定时任务失败!", e);
        }
    }

}
