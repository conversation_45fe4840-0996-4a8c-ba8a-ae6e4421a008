/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.bugly.core.constant.DataSourceConstant;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyAlarmConditionLink;
import vip.xiaonuo.bugly.modular.alarm.mapper.BuglyAlarmConditionLinkMapper;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyAlarmConditionLinkAddParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyAlarmConditionLinkEditParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyAlarmConditionLinkIdParam;
import vip.xiaonuo.bugly.modular.alarm.param.BuglyAlarmConditionLinkPageParam;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyAlarmConditionLinkService;

import java.util.List;

/**
 * 告警基本信息与告警条件关联表Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/01/20 14:55
 **/
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_BUGLY_MONITOR)
@Slf4j
public class BuglyAlarmConditionLinkServiceImpl extends ServiceImpl<BuglyAlarmConditionLinkMapper, BuglyAlarmConditionLink> implements BuglyAlarmConditionLinkService {

    @Override
    public Page<BuglyAlarmConditionLink> page(BuglyAlarmConditionLinkPageParam buglyAlarmConditionLinkPageParam) {
        QueryWrapper<BuglyAlarmConditionLink> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(buglyAlarmConditionLinkPageParam.getConditionId())) {
            queryWrapper.lambda().eq(BuglyAlarmConditionLink::getConditionId, buglyAlarmConditionLinkPageParam.getConditionId());
        }
        if (ObjectUtil.isNotEmpty(buglyAlarmConditionLinkPageParam.getValueType())) {
            queryWrapper.lambda().eq(BuglyAlarmConditionLink::getValueType, buglyAlarmConditionLinkPageParam.getValueType());
        }
        if (ObjectUtil.isNotEmpty(buglyAlarmConditionLinkPageParam.getStatus())) {
            queryWrapper.lambda().eq(BuglyAlarmConditionLink::getStatus, buglyAlarmConditionLinkPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(buglyAlarmConditionLinkPageParam.getSortField(), buglyAlarmConditionLinkPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(buglyAlarmConditionLinkPageParam.getSortOrder());
            queryWrapper.orderBy(true, buglyAlarmConditionLinkPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(buglyAlarmConditionLinkPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(BuglyAlarmConditionLink::getAlarmId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public void add(BuglyAlarmConditionLinkAddParam buglyAlarmConditionLinkAddParam) {
        BuglyAlarmConditionLink buglyAlarmConditionLink = BeanUtil.toBean(buglyAlarmConditionLinkAddParam, BuglyAlarmConditionLink.class);
        this.save(buglyAlarmConditionLink);
    }

    @Override
    public void edit(BuglyAlarmConditionLinkEditParam buglyAlarmConditionLinkEditParam) {
        BuglyAlarmConditionLink buglyAlarmConditionLink = this.queryEntity(buglyAlarmConditionLinkEditParam.getAlarmId());
        BeanUtil.copyProperties(buglyAlarmConditionLinkEditParam, buglyAlarmConditionLink);
        this.updateById(buglyAlarmConditionLink);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BuglyAlarmConditionLinkIdParam> buglyAlarmConditionLinkIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(buglyAlarmConditionLinkIdParamList, BuglyAlarmConditionLinkIdParam::getAlarmId));
    }

    @Override
    public BuglyAlarmConditionLink detail(BuglyAlarmConditionLinkIdParam buglyAlarmConditionLinkIdParam) {
        return this.queryEntity(buglyAlarmConditionLinkIdParam.getAlarmId());
    }

    @Override
    public BuglyAlarmConditionLink queryEntity(String id) {
        BuglyAlarmConditionLink buglyAlarmConditionLink = this.getById(id);
        if (ObjectUtil.isEmpty(buglyAlarmConditionLink)) {
            throw new CommonException("告警基本信息与告警条件关联表不存在，id值为：{}", id);
        }
        return buglyAlarmConditionLink;
    }

    @Override
    public Boolean deleteByAlarmId(String alarmId) {
        if (StringUtils.isBlank(alarmId)) {
            return Boolean.FALSE;
        }
        QueryWrapper<BuglyAlarmConditionLink> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BuglyAlarmConditionLink::getAlarmId, alarmId);
        return this.remove(queryWrapper);
    }
}
