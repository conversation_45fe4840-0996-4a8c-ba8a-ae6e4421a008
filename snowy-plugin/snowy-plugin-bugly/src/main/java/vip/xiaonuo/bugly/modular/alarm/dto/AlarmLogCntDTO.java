package vip.xiaonuo.bugly.modular.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 告警次数传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/23 15:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "告警次数传输对象")
public class AlarmLogCntDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 告警信息id
     */
    @ApiModelProperty(value = "告警信息ID", name = "alarmId", required = true)
    private String alarmId;

    /**
     * 告警条件ID
     */
    @ApiModelProperty(value = "告警条件ID", name = "conditionId", required = false)
    private String conditionId;

    /**
     * 告警日志记录次数
     */
    @ApiModelProperty(value = "告警日志记录次数", name = "cnt", required = true)
    private Long cnt;
}
