package vip.xiaonuo.bugly.modular.query.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.bugly.modular.monitor.entity.MonitorBuglyHour;
import vip.xiaonuo.bugly.modular.query.service.MisApiQueryService;
import vip.xiaonuo.bugly.modular.syncInfo.entity.AppInfo;
import vip.xiaonuo.common.pojo.CommonResult;

import java.util.*;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/1/18 16:22
 */
@Api(tags = "MIS API数据查询")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@RequestMapping("bugly/query/misApiQuery")
@Validated
public class MisApiQueryController {

    @Autowired
    private MisApiQueryService misApiQueryService;

    /**
     * 根据时间查询手游异常数据(天或小时)(汇总数据)(手游)
     *
     * @param appId     应用ID
     * @param dayHour   时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour =day按天统计，=hour 按小时统计，默认小时
     * @return 异常数据(天或小时)(汇总数据)(手游)
     */
    @ApiOperation(value = "手游异常数据(天或小时)(汇总数据)(手游)", notes = "根据时间查询手游异常数据(天或小时)(汇总数据)(手游)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", paramType = "query", required = true, dataType = "String")
            , @ApiImplicitParam(name = "dayHour", value = "时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "=day按天统计，=hour 按小时统计，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("listExceptionDataMonitor")
    public CommonResult<List<MonitorBuglyHour>> listExceptionDataMonitor(
            @RequestParam(value = "appId", required = false) String appId
            , @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppId(appId);
        return CommonResult.data(misApiQueryService.listExceptionDataMonitor(appInfo, dayHour, dayOrHour));
    }

    /**
     * 根据时间查询手游心跳数据(天或小时)(汇总数据)(手游)
     *
     * @param appId     应用ID
     * @param dayHour   时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour =day按天统计，=hour 按小时统计，默认小时
     * @return 心跳数据(天或小时)(汇总数据)(手游)
     */
    @ApiOperation(value = "手游心跳数据(天或小时)(汇总数据)(手游)", notes = "根据时间查询手游心跳数据(天或小时)(汇总数据)(手游)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", paramType = "query", required = true, dataType = "String")
            , @ApiImplicitParam(name = "dayHour", value = "时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "=day按天统计，=hour 按小时统计，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("listHeartbeatDataMonitor")
    public CommonResult<List<MonitorBuglyHour>> listHeartbeatDataMonitor(
            @RequestParam(value = "appId", required = false) String appId
            , @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppId(appId);
        return CommonResult.data(misApiQueryService.listHeartbeatDataMonitor(appInfo, dayHour, dayOrHour));
    }

    /**
     * 根据时间查询H5异常和心跳数据(天或小时)(汇总数据)(H5)
     *
     * @param appId     应用ID
     * @param dayHour   时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour =day按天统计，=hour 按小时统计，默认小时
     * @return 异常和心跳数据(天或小时)(汇总数据)(H5)
     */
    @ApiOperation(value = "H5异常和心跳数据(天或小时)(汇总数据)(H5)", notes = "根据时间查询H5异常和心跳数据(天或小时)(汇总数据)(H5)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", paramType = "query", required = true, dataType = "String")
            , @ApiImplicitParam(name = "dayHour", value = "时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "=day按天统计，=hour 按小时统计，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("listH5ExceptionAndHeartbeatDataMonitor")
    public CommonResult<List<MonitorBuglyHour>> listH5ExceptionAndHeartbeatDataMonitor(
            @RequestParam(value = "appId", required = false) String appId
            , @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppId(appId);
        return CommonResult.data(misApiQueryService.listH5ExceptionAndHeartbeatDataMonitor(appInfo, dayHour, dayOrHour));
    }

    /**
     * 根据时间查询H5心跳数据(天或小时)(汇总数据)(H5)
     *
     * @param appId     应用ID
     * @param dayHour   时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour =day按天统计，=hour 按小时统计，默认小时
     * @return 心跳数据(天或小时)(汇总数据)
     */
    @ApiOperation(value = "H5心跳数据(天或小时)(汇总数据)(H5)", notes = "根据时间查询H5心跳数据(天或小时)(汇总数据)(H5)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", paramType = "query", required = true, dataType = "String")
            , @ApiImplicitParam(name = "dayHour", value = "时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "=day按天统计，=hour 按小时统计，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("listH5HeartbeatDataMonitor")
    public CommonResult<List<MonitorBuglyHour>> listH5HeartbeatDataMonitor(
            @RequestParam(value = "appId", required = false) String appId
            , @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppId(appId);
        return CommonResult.data(misApiQueryService.listH5HeartbeatDataMonitor(appInfo, dayHour, dayOrHour));
    }

    @ApiOperation(value = "获取异常列表(手游)", notes = "获取异常列表(手游)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", paramType = "query", required = true, dataType = "String")
            , @ApiImplicitParam(name = "dayHour", value = "时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "=day按天统计，=hour 按小时统计，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
            , @ApiImplicitParam(name = "exceptionTypes", value = "异常类型(非H5应用)(可选：crash,caton,error,report,anr,unknown 多个使用逗号\",\"分隔)", paramType = "query", required = false, dataType = "String")
    })
    @GetMapping("exceptionDetailApp")
    public CommonResult<List<JSONObject>> exceptionDetailApp(
            @RequestParam(value = "appId", required = false) String appId
            , @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour
            , @RequestParam(value = "exceptionTypes", required = false) String exceptionTypes) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppId(appId);

        List<String> exceptionTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(StringUtils.deleteWhitespace(exceptionTypes))) {
            exceptionTypeList = Arrays.asList(StringUtils.splitPreserveAllTokens(StringUtils.deleteWhitespace(exceptionTypes), ","));
        }

        return CommonResult.data(misApiQueryService.exceptionDetailApp(appInfo, dayHour, dayOrHour, new HashSet<>(exceptionTypeList)));
    }

    @ApiOperation(value = "获取异常列表(H5)", notes = "获取异常列表(H5)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用ID", paramType = "query", required = true, dataType = "String")
            , @ApiImplicitParam(name = "dayHour", value = "时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss", paramType = "query", required = false, dataType = "String")
            , @ApiImplicitParam(name = "dayOrHour", value = "=day按天统计，=hour 按小时统计，默认小时", paramType = "query", required = false, dataType = "dayOrHour")
    })
    @GetMapping("exceptionDetailH5")
    public CommonResult<List<JSONObject>> exceptionDetailH5(
            @RequestParam(value = "appId", required = false) String appId
            , @RequestParam(value = "dayHour", required = false) String dayHour
            , @RequestParam(value = "dayOrHour", required = false) String dayOrHour) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppId(appId);
        return CommonResult.data(misApiQueryService.exceptionDetailH5(appInfo, dayHour, dayOrHour));
    }

}
