package vip.xiaonuo.enemy;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 线索功能测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/31 15:30
 */
@SpringBootTest
public class XiansuoTest {

    @Test
    public void testXiansuoMessage() {
        // 构造测试数据
        String testMessage = """
                {
                    "item_info": {
                        "price": 15.38,
                        "name": "🔥iOS曙光英雄【黑豹】天卡/全图内透/免签名",
                        "stock": "28",
                        "tid": "46782",
                        "sales": "0",
                        "cid": "677",
                        "desc": "iOS官方网盘：https://ios888.icu<br /> iOS官方网盘2：https://zs777.icu<br /> <br />"
                    },
                    "domain": "lilingyu1",
                    "channel": "xiansuo",
                    "keyword": "曙光",
                    "url": "https://li.lilingyu1.cn/?v=1753259583620"
                }
                """;

        JSONObject msgJSONObject = JSONObject.parseObject(testMessage);
        System.out.println("测试消息：" + msgJSONObject.toJSONString());
        
        // 这里可以调用EnemyMsgService的add方法进行测试
        // enemyMsgService.add(msgJSONObject);
    }
}
