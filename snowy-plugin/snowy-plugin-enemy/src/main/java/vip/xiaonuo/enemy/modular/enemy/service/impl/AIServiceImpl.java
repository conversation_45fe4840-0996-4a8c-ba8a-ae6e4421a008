package vip.xiaonuo.enemy.modular.enemy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vip.xiaonuo.enemy.core.constant.DataSourceConstant;
import vip.xiaonuo.enemy.modular.enemy.entity.EnemyMsg;
import vip.xiaonuo.enemy.modular.enemy.service.AIService;
import vip.xiaonuo.enemy.modular.enemy.service.EnemyMsgService;
import vip.xiaonuo.enemy.modular.message.constant.MessageConstant;
import vip.xiaonuo.enemy.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.enemy.modular.message.service.MessageService;
import vip.xiaonuo.enemy.modular.remote.service.AIRemoteService;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 14:14
 */
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_ENEMY_MESSAGE)
@Slf4j
public class AIServiceImpl implements AIService {

    @Autowired
    private AIRemoteService aiRemoteService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private EnemyMsgService enemyMsgService;

    @Value("${app-config.robot.enemy.msg.token}")
    private String enemyMsgToken;

    @Override
    public void setAiDetectResult(String msgId, String msg) {
        //        log.info("--------------------- 准备进入AI接口调用线程,msgId=[{}] ---------------------", msgId);
        ThreadUtil.execAsync(() -> {
            try {
                EnemyMsg enemyMsg = enemyMsgService.queryEntity(msgId);
//                log.info("--------------------- AI接口调用 开始 ---------------------");
                LocalDateTime startTime = LocalDateTime.now();
                JSONObject params = new JSONObject();
                params.put("msgId", msgId);
                params.put("rawtext", msg);
                // 自定义超时时间
                Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 5);
                JSONObject remoteResult = aiRemoteService.detectTxt(options, params);
                // result取值：[违规、不违规、未找到合理依据进行判断]
                if (remoteResult != null && StringUtils.isNotBlank(remoteResult.getString("result"))) {
                    List<String> abResultList = new ArrayList<>();
                    List<String> abResultZhList = new ArrayList<>();
                    String detectTxtResult = remoteResult.getString("result");
                    if (StringUtils.equalsIgnoreCase(detectTxtResult, "不违规") || StringUtils.equalsIgnoreCase(detectTxtResult, "正常")) {
                        abResultList.add("2");
                        abResultZhList.add(detectTxtResult);
                    } else if (StringUtils.equalsIgnoreCase(detectTxtResult, "未找到合理依据进行判断") || StringUtils.equalsIgnoreCase(detectTxtResult, "未知") || StringUtils.equalsIgnoreCase(detectTxtResult, "其他")) {
                        abResultList.add("3");
                        abResultZhList.add(detectTxtResult);
                    } else {
                        abResultList.add("1");
                        abResultZhList.add(detectTxtResult);
                    }
                    enemyMsg.setAbResult(JSONObject.toJSONString(abResultList));
                    enemyMsg.setAbContent(remoteResult.getString("reason"));
                    enemyMsg.setAbFeedback(StringUtils.join(abResultZhList,","));
                    enemyMsgService.updateById(enemyMsg);

                    // 是否发送告警
                    Boolean isSend = Boolean.FALSE;
                    String sendDDRobotMessageTemplate = null;
                    if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "tieba")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_TIEBA_MD_TEMPLATE;
                        // (贴吧)内容异常则发送告警
                        if (CollectionUtils.containsAny(abResultList, "1")) {
                            isSend = Boolean.TRUE;
                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "goofish")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_GOOFISH_MD_TEMPLATE;
                        // (闲鱼)内容异常则发送告警
                        if (CollectionUtils.containsAny(abResultList, "1")) {
                            isSend = Boolean.TRUE;
                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "wechat")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_WECHAT_MD_TEMPLATE;
                        // (微信群)内容异常则发送告警
                        if (CollectionUtils.containsAny(abResultList, "1")) {
                            isSend = Boolean.TRUE;
                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "qq")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_QQ_MD_TEMPLATE;
                        // (QQ群)内容异常则发送告警
                        if (CollectionUtils.containsAny(abResultList, "1")) {
                            isSend = Boolean.TRUE;
                        }
                    } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "xiansuo")) {
                        sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE;
                        // 线索全部发送告警
                        isSend = Boolean.TRUE;
                    }
                    if (isSend) {
                        JSONObject templateData = JSONObject.parseObject(JSONObject.toJSONString(enemyMsg));
                        templateData.put("abResultZh", StringUtils.join(abResultZhList, ","));
                        templateData.put("data", JSONObject.parseObject(enemyMsg.getDataJson()));
                        templateData.put("sendTime", DateUtil.now());
                        SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(sendDDRobotMessageTemplate, templateData, "markdown", enemyMsgToken);
                        messageService.sendDDRobot(sendRobotDTO);
                    }
                } else {
                    log.error("远程调用AI文本识别接口报错,msgId=[{}],远程返回结果=[{}],msg=[{}]", msgId, JSONObject.toJSONString(remoteResult), msg);
                }
                LocalDateTime endTime = LocalDateTime.now();
                Duration duration = Duration.between(startTime, endTime);
                log.info(String.format("--------------------- AI接口调用 结束(耗时=[%s]ms) ---------------------", duration.toMillis()));
            } catch (Exception e) {
                log.error("异步更新文本识别结果失败,msgId=[{}]", msgId, e);
            }
        }, false);
    }
}
