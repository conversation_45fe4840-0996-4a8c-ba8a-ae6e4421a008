/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.enemy.modular.enemy.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.enemy.modular.enemy.entity.EnemyMsg;
import vip.xiaonuo.enemy.modular.enemy.dto.EnemyMsgAiDetectStatDTO;
import vip.xiaonuo.enemy.modular.enemy.param.*;

import java.util.List;

/**
 * 敌情线索-线索表Service接口
 *
 * <AUTHOR>
 * @date 2024/12/13 13:34
 **/
public interface EnemyMsgService extends IService<EnemyMsg> {

    /**
     * 获取敌情线索-线索表分页
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     */
    Page<EnemyMsg> page(EnemyMsgPageParam enemyMsgPageParam);

    /**
     * 获取敌情线索-线索列表
     *
     * @param enemyMsgListParam 敌情线索列表查询参数
     * @return 敌情线索列表
     */
    List<EnemyMsg> list(EnemyMsgListParam enemyMsgListParam);

    /**
     * 添加敌情线索-线索表
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     */
    void add(EnemyMsgAddParam enemyMsgAddParam);

    /**
     * 编辑敌情线索-线索表
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     */
    void edit(EnemyMsgEditParam enemyMsgEditParam);

    /**
     * 删除敌情线索-线索表
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     */
    void delete(List<EnemyMsgIdParam> enemyMsgIdParamList);

    /**
     * 获取敌情线索-线索表详情
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     */
    EnemyMsg detail(EnemyMsgIdParam enemyMsgIdParam);

    /**
     * 获取敌情线索-AI去重线索列表(待去重)
     *
     * @param enemyMsgListAiParam AI去重线索列表(待去重)获取参数
     * @return AI去重线索列表(待去重)
     */
    List<EnemyMsg> listAiDistinct(EnemyMsgListAiParam enemyMsgListAiParam);

    /**
     * 获取敌情线索-AI检测线索列表(待检测)
     *
     * @param enemyMsgListAiParam AI检测线索列表(待检测)获取参数
     * @return AI检测线索列表(待检测)
     */
    List<EnemyMsg> listAiDetect(EnemyMsgListAiParam enemyMsgListAiParam);

    /**
     * 新增敌情线索-AI去重结果
     *
     * @param enemyMsgAiResultParamList 敌情线索-AI检测(去重)结果参数
     */
    void addAiDistinct(List<EnemyMsgAiResultParam> enemyMsgAiResultParamList);

    /**
     * 新增敌情线索-AI检测结果
     *
     * @param enemyMsgAiResultParamList 敌情线索-AI检测(去重)结果参数
     */
    void addAiDetect(List<EnemyMsgAiResultParam> enemyMsgAiResultParamList);

    /**
     * 获取敌情线索-线索表详情
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     **/
    EnemyMsg queryEntity(String id);

    /**
     * 添加敌情线索-线索表
     *
     * <AUTHOR>
     * @date 2024/12/13 13:34
     */
    void add(JSONObject msgJSONObject);

    /**
     * 统计AI检测结果每日数量
     *
     * @param enemyMsgAiDetectStatParam AI检测结果统计参数
     * @return AI检测结果统计数据列表
     */
    List<EnemyMsgAiDetectStatDTO> statAiDetectResult(EnemyMsgAiDetectStatParam enemyMsgAiDetectStatParam);
}
