package vip.xiaonuo.enemy.modular.message.constant;

import java.io.Serializable;

/**
 * 消息相关常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/16 12:38
 */
public class MessageConstant implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 发送监控告警-信鸽消息模板
     */
    public static final String SEND_XINGE_MONITOR_MESSAGE_TEMPLATE = "enemy/send_xinge_monitor_message_template.ftl";

    /**
     * 发送监控告警-邮件消息模板
     */
    public static final String SEND_EMAIL_MONITOR_MESSAGE_TEMPLATE = "enemy/send_email_monitor_message_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_TEMPLATE = "enemy/send_robot_enemy_message_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板(闲鱼)
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_GOOFISH_MD_TEMPLATE = "enemy/send_robot_enemy_message_goofish_md_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板(贴吧)
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_TIEBA_MD_TEMPLATE = "enemy/send_robot_enemy_message_tieba_md_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板(微信群)
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_WECHAT_MD_TEMPLATE = "enemy/send_robot_enemy_message_wechat_md_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板(QQ群)
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_QQ_MD_TEMPLATE = "enemy/send_robot_enemy_message_qq_md_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板(闲鱼)(去重)
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_GOOFISH_DISTINCT_MD_TEMPLATE = "enemy/send_robot_enemy_message_goofish_distinct_md_template.ftl";

    /**
     * 发送监控告警-机器人群消息模板(线索)
     */
    public static final String SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE = "enemy/send_robot_enemy_message_xiansuo_md_template.ftl";

    /**
     * 发送日报-信鸽消息模板
     */
    public static final String SEND_XINGE_DAY_REPORT_MESSAGE_TEMPLATE = "enemy/send_xinge_day_report_message_template.ftl";

    /**
     * 发送日报-邮件消息模板
     */
    public static final String SEND_EMAIL_DAY_REPORT_MESSAGE_TEMPLATE = "enemy/send_email_day_report_message_template.ftl";

    /**
     * 发送AI检测结果-邮件消息模板
     */
    public static final String SEND_EMAIL_ENEMY_MESSAGE_AI_DETECT_TEMPLATE = "enemy/send_email_enemy_message_ai_detect_template.ftl";
}
