package vip.xiaonuo.enemy.modular.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 发送信鸽消息传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/3/1 16:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "发送信鸽消息传输对象")
public class SendXingeDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", name = "source")
    private String source;

    /**
     * 接收类型，1：接收人为用户 ID，0：接收人为信鸽 ID
     */
    @ApiModelProperty(value = "接收类型，1：接收人为用户ID，0：接收人为信鸽ID", name = "recType")
    private Integer recType;

    /**
     * 接收人列表
     */
    @ApiModelProperty(value = "接收人列表", name = "receivers")
    private List<String> receivers;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题", name = "subject")
    private String subject;

    /**
     * 文本内容，需要base64编码，utf-8编码
     */
    @ApiModelProperty(value = "文本内容，需要base64编码，utf-8编码", name = "body")
    private String body;

    /**
     * 文本内容，未进行base64编码
     */
    @ApiModelProperty(value = "文本内容，未进行base64编码", name = "bodyNotBase64")
    private String bodyNotBase64;

    public String getBody() {
        if (StringUtils.isEmpty(body) && StringUtils.isNotEmpty(bodyNotBase64)) {
            body = Base64.encodeBase64String(bodyNotBase64.getBytes(StandardCharsets.UTF_8));
        }
        return body;
    }
}
