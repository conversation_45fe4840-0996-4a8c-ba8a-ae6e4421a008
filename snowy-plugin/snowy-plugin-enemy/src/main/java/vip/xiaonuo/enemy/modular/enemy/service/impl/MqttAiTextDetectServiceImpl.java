package vip.xiaonuo.enemy.modular.enemy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import vip.xiaonuo.enemy.core.constant.DataSourceConstant;
import vip.xiaonuo.enemy.modular.enemy.dto.AiTextDetectRequestDTO;
import vip.xiaonuo.enemy.modular.enemy.dto.AiTextDetectResponseDTO;
import vip.xiaonuo.enemy.modular.enemy.entity.EnemyMsg;
import vip.xiaonuo.enemy.modular.enemy.service.EnemyMsgService;
import vip.xiaonuo.enemy.modular.enemy.service.MqttAiTextDetectService;
import vip.xiaonuo.enemy.core.config.MqttTextConfig;
import vip.xiaonuo.enemy.modular.message.constant.MessageConstant;
import vip.xiaonuo.enemy.modular.message.dto.SendRobotDTO;
import vip.xiaonuo.enemy.modular.message.service.MessageService;
import vip.xiaonuo.enemy.modular.remote.service.AIRemoteService;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * MQTT AI文本识别服务实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 17:10
 */
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_ENEMY_MESSAGE)
@Slf4j
public class MqttAiTextDetectServiceImpl implements MqttAiTextDetectService {

    @Autowired(required = false)
    @Qualifier("mqttTextOutboundChannel")
    private MessageChannel mqttTextOutboundChannel;

    @Autowired(required = false)
    private MqttTextConfig mqttTextConfig;

    @Autowired
    private EnemyMsgService enemyMsgService;

    @Autowired
    private AIRemoteService aiRemoteService;

    @Autowired
    private MessageService messageService;

    @Value("${app-config.robot.enemy.msg.token}")
    private String enemyMsgToken;

    @Value("${app-config.robot.enemy.msg.tokenOfShuguang}")
    private String enemyMsgTokenShuguang;

    @Override
    public void sendAiTextDetectRequest(String msgId, String msg) {
        if (mqttTextConfig == null || !mqttTextConfig.isMqttTextEnabled() || mqttTextOutboundChannel == null) {
            log.warn("MQTT文本识别未启用或未配置，跳过AI文本识别请求发送，msgId=[{}]", msgId);
            throw new RuntimeException("MQTT文本识别未启用或未配置");
        }

        try {
            AiTextDetectRequestDTO requestDTO = new AiTextDetectRequestDTO();
            requestDTO.setMsgId(msgId);
            requestDTO.setRawtext(msg);
            requestDTO.setTimestamp(System.currentTimeMillis());
            requestDTO.setRequestId(IdUtil.fastSimpleUUID());

            String messagePayload = JSONObject.toJSONString(requestDTO);

            Message<String> message = MessageBuilder
                    .withPayload(messagePayload)
                    .setHeader(MqttHeaders.TOPIC, mqttTextConfig.getAiTextRequestTopic())
                    .setHeader(MqttHeaders.QOS, 2)
                    .setHeader(MqttHeaders.RETAINED, false)
                    .build();

            mqttTextOutboundChannel.send(message);
            log.info("AI文本识别请求已发送到MQTT，msgId=[{}], requestId=[{}]", msgId, requestDTO.getRequestId());
        } catch (Exception e) {
            log.error("发送AI文本识别请求到MQTT失败，msgId=[{}]", msgId, e);
            throw e;
        }
    }


    @Override
    public void handleAiTextDetectResponse(AiTextDetectResponseDTO responseDTO) {
        if (responseDTO == null || StringUtils.isBlank(responseDTO.getMsgId())) {
            log.warn("AI文本识别响应数据无效：{}", JSONObject.toJSONString(responseDTO));
            return;
        }

        try {
            EnemyMsg enemyMsg = enemyMsgService.queryEntity(responseDTO.getMsgId());
            if (enemyMsg == null) {
                log.warn("未找到对应的消息记录，msgId=[{}]", responseDTO.getMsgId());
                return;
            }

            if (responseDTO.getSuccess() == null || responseDTO.getSuccess()) {
                // 处理识别结果
                if (StringUtils.isNotBlank(responseDTO.getResult())) {
                    List<String> abResultList = new ArrayList<>();
                    List<String> abResultZhList = new ArrayList<>();
                    String detectTxtResult = responseDTO.getResult();

                    if (StringUtils.equalsIgnoreCase(detectTxtResult, "不违规") || StringUtils.equalsIgnoreCase(detectTxtResult, "正常")) {
                        abResultList.add("2");
                        abResultZhList.add(detectTxtResult);
                    } else if (StringUtils.equalsIgnoreCase(detectTxtResult, "未找到合理依据进行判断") || StringUtils.equalsIgnoreCase(detectTxtResult, "未知") || StringUtils.equalsIgnoreCase(detectTxtResult, "其他")) {
                        abResultList.add("3");
                        abResultZhList.add(detectTxtResult);
                    } else {
                        abResultList.add("1");
                        abResultZhList.add(detectTxtResult);
                    }

                    enemyMsg.setAbResult(JSONObject.toJSONString(abResultList));
                    enemyMsg.setAbContent(responseDTO.getReason());
                    enemyMsg.setAbFeedback(StringUtils.join(abResultZhList, ","));
                    enemyMsgService.updateById(enemyMsg);

                    // 发送告警逻辑
                    sendAlarmIfNeeded(enemyMsg, abResultList, abResultZhList);

                    log.info("AI文本识别结果已更新，msgId=[{}], requestId=[{}], result=[{}]",
                            responseDTO.getMsgId(), responseDTO.getRequestId(), detectTxtResult);
                }
            } else {
                log.error("AI文本识别失败，msgId=[{}], requestId=[{}], error=[{}]",
                        responseDTO.getMsgId(), responseDTO.getRequestId(), responseDTO.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("处理AI文本识别响应失败，msgId=[{}]", responseDTO.getMsgId(), e);
        }
    }

    @Override
    public AiTextDetectResponseDTO detectTextDirect(AiTextDetectRequestDTO requestDTO) {
        AiTextDetectResponseDTO responseDTO = new AiTextDetectResponseDTO();
        responseDTO.setMsgId(requestDTO.getMsgId());
        responseDTO.setRequestId(requestDTO.getRequestId());
        responseDTO.setTimestamp(System.currentTimeMillis());

        try {
            LocalDateTime startTime = LocalDateTime.now();
            JSONObject params = new JSONObject();
            params.put("msgId", requestDTO.getMsgId());
            params.put("rawtext", requestDTO.getRawtext());

            // 自定义超时时间
            Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 5);
            JSONObject remoteResult = aiRemoteService.detectTxt(options, params);

            if (remoteResult != null && StringUtils.isNotBlank(remoteResult.getString("result"))) {
                responseDTO.setSuccess(true);
                responseDTO.setResult(remoteResult.getString("result"));
                responseDTO.setReason(remoteResult.getString("reason"));
            } else {
                responseDTO.setSuccess(false);
                responseDTO.setErrorMessage("AI文本识别接口返回结果为空");
            }

            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            log.info("直接调用AI文本识别接口完成，耗时=[{}]ms，msgId=[{}]",
                    duration.toMillis(), requestDTO.getMsgId());

        } catch (Exception e) {
            log.error("直接调用AI文本识别接口失败，msgId=[{}]", requestDTO.getMsgId(), e);
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage(e.getMessage());
        }

        return responseDTO;
    }

    /**
     * 发送告警（如果需要）
     */
    private void sendAlarmIfNeeded(EnemyMsg enemyMsg, List<String> abResultList, List<String> abResultZhList) {
        try {
            Boolean isSend = Boolean.FALSE;
            String sendDDRobotMessageTemplate = null;

            if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "tieba")) {
                sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_TIEBA_MD_TEMPLATE;
                // (贴吧)内容异常则发送告警
                if (CollectionUtils.containsAny(abResultList, "1")) {
                    isSend = Boolean.TRUE;
                }
            } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "goofish")) {
                sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_GOOFISH_MD_TEMPLATE;
                // (闲鱼)内容异常则发送告警
                if (CollectionUtils.containsAny(abResultList, "1")) {
                    isSend = Boolean.TRUE;
                }
            } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "wechat")) {
                sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_WECHAT_MD_TEMPLATE;
                // (微信群)内容异常则发送告警
                if (CollectionUtils.containsAny(abResultList, "1")) {
                    isSend = Boolean.TRUE;
                }
            } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "qq")) {
                sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_QQ_MD_TEMPLATE;
                // (QQ群)内容异常则发送告警
                if (CollectionUtils.containsAny(abResultList, "1")) {
                    isSend = Boolean.TRUE;
                }
            } else if (StringUtils.equalsIgnoreCase(enemyMsg.getSourceType(), "xiansuo")) {
                sendDDRobotMessageTemplate = MessageConstant.SEND_ROBOT_ENEMY_MESSAGE_XIANSUO_MD_TEMPLATE;
                // 线索全部发送告警
                isSend = Boolean.TRUE;
            }

            if (isSend && StringUtils.isNotBlank(sendDDRobotMessageTemplate)) {
                JSONObject templateData = JSONObject.parseObject(JSONObject.toJSONString(enemyMsg));
                templateData.put("abResultZh", StringUtils.join(abResultZhList, ","));
                templateData.put("data", JSONObject.parseObject(enemyMsg.getDataJson()));
                templateData.put("sendTime", DateUtil.now());
                SendRobotDTO sendRobotDTO = messageService.generateSendDDRobotMessage(
                        sendDDRobotMessageTemplate, templateData, "markdown", enemyMsgToken);
                messageService.sendDDRobot(sendRobotDTO);
                // 曙光英雄另外单独发送
                if (StringUtils.containsIgnoreCase(enemyMsg.getHitKeyword(), "曙光") || StringUtils.containsIgnoreCase(enemyMsg.getContentName(), "曙光")) {
                    SendRobotDTO sendRobotDTOshuguang = messageService.generateSendDDRobotMessage(
                            sendDDRobotMessageTemplate, templateData, "markdown", enemyMsgTokenShuguang);
                    messageService.sendDDRobot(sendRobotDTOshuguang);
                }
                log.info("已发送告警消息，msgId=[{}]", enemyMsg.getMsgId());
            }
        } catch (Exception e) {
            log.error("发送告警消息失败，msgId=[{}]", enemyMsg.getMsgId(), e);
        }
    }
}
