package vip.xiaonuo.biz.modular.cloudcontroller.service;

import vip.xiaonuo.biz.modular.cloudcontroller.dto.AiDetectRequestDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.AiDetectResponseDTO;

/**
 * MQTT AI识别服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 15:20
 */
public interface MqttAiDetectService {

    /**
     * 发送AI识别请求到MQTT
     *
     * @param resultId         结果ID
     * @param fileIdOfLuaScreenShot 文件ID
     * @param base64OfLuaScreenShot Base64图片
     * @param deviceId              设备ID
     * @param day                   天
     */
    void sendAiDetectRequest(String resultId, String fileIdOfLuaScreenShot, String base64OfLuaScreenShot, String deviceId, String day);

    /**
     * 处理AI识别响应
     *
     * @param responseDTO AI识别响应
     */
    void handleAiDetectResponse(AiDetectResponseDTO responseDTO);

    /**
     * 直接调用AI识别接口
     *
     * @param requestDTO AI识别请求
     * @return AI识别响应
     */
    AiDetectResponseDTO detectDirect(AiDetectRequestDTO requestDTO);
}
