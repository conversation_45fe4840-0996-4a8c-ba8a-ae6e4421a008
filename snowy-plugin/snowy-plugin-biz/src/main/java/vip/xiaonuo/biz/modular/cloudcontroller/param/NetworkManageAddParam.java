package vip.xiaonuo.biz.modular.cloudcontroller.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 网络终端管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/14 10:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "网络终端管理-新增策略传输对象")
public class NetworkManageAddParam implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * (该字段非前端传入，后台根据调用接口添加)policyId(本次下发属于哪个policyId，通过policyId执行相应策略种类)
     */
//    @ApiModelProperty(value = "(该字段非前端传入，后台根据调用接口添加)policyId(本次下发属于哪个policyId，通过policyId执行相应策略种类)", required = false, position = 1)
//    private Integer policyId;

    /**
     * (该字段非前端传入，后台根据调用接口添加)luaTask(该参数属于哪个luaTask)
     */
//    @ApiModelProperty(value = "(该字段非前端传入，后台根据调用接口添加)luaTask(该参数属于哪个luaTask)", required = false, position = 1)
//    private String luaTask;

    /**
     * (该字段非前端传入，后台根据调用接口添加)operation,操作类型，add:新增策略，del:删除策略
     */
    @ApiModelProperty(value = "(该字段非前端传入，后台根据调用接口添加)operation,操作类型，add:新增策略，del:删除策略", required = false, position = 1)
    private String operation;

    /**
     * 规则名
     */
    @ApiModelProperty(value = "规则名", required = true, position = 1)
    @NotBlank(message = "规则名不能为空")
    private String name;
    /**
     * 电脑ID
     */
    @ApiModelProperty(value = "电脑ID", required = true, position = 1)
    @NotBlank(message = "电脑ID不能为空")
    private String pcId;

    /**
     * 策略ID
     */
    @ApiModelProperty(value = "策略ID", required = true, position = 1)
    @NotBlank(message = "策略ID不能为空")
    private String ruleId;
    /**
     * 电脑名称
     */
    @ApiModelProperty(value = "电脑名称", required = true, position = 1)
    @NotBlank(message = "电脑名称不能为空")
    private String pcName;

    /**
     * 方向,in|out
     */
    @ApiModelProperty(value = "方向,in|out", required = true, position = 2)
    @NotBlank(message = "方向不能为空")
    private String direction;

    /**
     * 程序名称
     */
    @ApiModelProperty(value = "程序名称,any|unity", required = true, position = 3)
    @NotBlank(message = "程序名称不能为空")
    private String programName;

    /**
     * 程序匹配类型，clear:精确 unClear:模糊
     */
    @ApiModelProperty(value = "程序匹配类型，clear:精确 unClear:模糊", required = true, position = 4)
    @NotBlank(message = "程序匹配类型不能为空(clear:精确 unClear:模糊)")
    private String programMatchMode;

    /**
     * 程序目录，任意路径填any
     */
    @ApiModelProperty(value = "程序目录，任意路径填any", required = true, position = 5)
    @NotBlank(message = "程序目录不能为空（任意路径填any）")
    private String programDir;

    /**
     * 协议类型，tcp|udp
     */
    @ApiModelProperty(value = "协议类型，tcp|udp", position = 6)
    private String protocol;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号", position = 7)
    private String protocolPort;

    /**
     * 连接类型，allow|block
     */
    @ApiModelProperty(value = "连接类型，allow|block", required = true, position = 8)
    @NotBlank(message = "连接类型不能为空(allow|block)")
    private String action;

    /**
     * 应用场景，多选(用逗号隔开)public|private|domain|any
     */
    @ApiModelProperty(value = "应用场景，多选(用逗号隔开)public|private|domain|any", required = true, position = 9)
    @NotBlank(message = "应用场景不能为空(多选(用逗号隔开)public|private|domain|any)")
    private String scene;

    /**
     * IP白名单，格式为ip1,ip2,ip3-ip4,ip5，其中ip4必须大于ip3（以A.B.C.D中A是最高位，D是最低位来看），如果是所有IP，填any
     */
    @ApiModelProperty(value = "IP白名单，格式为ip1,ip2,ip3-ip4,ip5，其中ip4必须大于ip3（以A.B.C.D中A是最高位，D是最低位来看），如果是所有IP，填any", required = true, position = 10)
    @NotBlank(message = "IP白名单不能为空(格式为ip1,ip2,ip3-ip4,ip5，其中ip4必须大于ip3（以A.B.C.D中A是最高位，D是最低位来看），如果是所有IP，填any)")
    private String ips;
    /**
     * 是否反选，true|false
     */
    @ApiModelProperty(value = "是否反选，true|false", position = 11)
    @NotBlank(message = "是否反选不能为空(true|false)")
    private String isInvert;
}
