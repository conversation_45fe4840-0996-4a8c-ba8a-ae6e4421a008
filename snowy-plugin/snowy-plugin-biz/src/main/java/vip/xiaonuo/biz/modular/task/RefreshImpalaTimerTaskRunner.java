package vip.xiaonuo.biz.modular.task;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import vip.xiaonuo.biz.modular.remote.service.DataRemoteService;
import vip.xiaonuo.common.enums.CommonExceptionEnum;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 刷新impala元数据定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/3/13 20:42
 */
@Component
@Slf4j
public class RefreshImpalaTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private DataRemoteService dataRemoteService;

    @Override
    public void action() {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行刷新impala元数据定时任务......");
            CommonResult<Boolean> result = dataRemoteService.refreshMpsRiskTag();
            Boolean importResult = CommonExceptionEnum.OK200.getCode().equals(result.getCode()) && BooleanUtils.toBooleanDefaultIfNull(result.getData(), Boolean.FALSE);
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            if (importResult) {
                log.info(String.format("执行刷新impala元数据定时任务成功,耗时=[%s]ms。", duration.toMillis()));
            } else {
                log.warn(String.format("执行刷新impala元数据定时任务失败,远程调用返回结果=[%s]，耗时=[%s]ms。", JSONObject.toJSONString(result), duration.toMillis()));
            }
        } catch (Exception e) {
            log.error("执行刷新impala元数据定时任务失败!", e);
        }
    }
}
