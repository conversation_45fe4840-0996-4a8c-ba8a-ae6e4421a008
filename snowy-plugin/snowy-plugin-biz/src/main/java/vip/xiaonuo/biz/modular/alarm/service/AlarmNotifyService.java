/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.alarm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import vip.xiaonuo.biz.modular.alarm.entity.AlarmNotify;
import vip.xiaonuo.biz.modular.alarm.param.AlarmNotifyAddParam;
import vip.xiaonuo.biz.modular.alarm.param.AlarmNotifyEditParam;
import vip.xiaonuo.biz.modular.alarm.param.AlarmNotifyIdParam;
import vip.xiaonuo.biz.modular.alarm.param.AlarmNotifyPageParam;

import java.util.List;
import java.util.Map;

/**
 * 告警通知信息Service接口
 *
 * <AUTHOR>
 * @date 2025/02/18 17:14
 **/
public interface AlarmNotifyService extends IService<AlarmNotify> {

    /**
     * 获取告警通知信息分页
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     */
    Page<AlarmNotify> page(AlarmNotifyPageParam alarmNotifyPageParam);

    /**
     * 添加告警通知信息
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     */
    void add(AlarmNotifyAddParam alarmNotifyAddParam);

    /**
     * 编辑告警通知信息
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     */
    void edit(AlarmNotifyEditParam alarmNotifyEditParam);

    /**
     * 删除告警通知信息
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     */
    void delete(List<AlarmNotifyIdParam> alarmNotifyIdParamList);

    /**
     * 获取告警通知信息详情
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     */
    AlarmNotify detail(AlarmNotifyIdParam alarmNotifyIdParam);

    /**
     * 获取告警通知信息详情
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     **/
    AlarmNotify queryEntity(String id);

    /**
     * 编辑告警通知信息状态
     *
     * @param id     告警通知信息ID
     * @param status 新状态
     */
    void editStatus(String id, String status);

    /**
     * 获取告警通知信息Map(key为gameId)
     *
     * <AUTHOR>
     * @date 2025/02/18 17:14
     */
    Map<Long, AlarmNotify> getGameIdAlarmNotifyMap();
}
