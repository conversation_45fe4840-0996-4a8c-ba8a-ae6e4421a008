/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.cloudcontroller.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.DataMatchResultDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.RuleMatchResultDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.PolicyLimit;
import vip.xiaonuo.biz.modular.cloudcontroller.param.*;
import vip.xiaonuo.biz.modular.cloudcontroller.service.PolicyLimitService;
import vip.xiaonuo.biz.modular.cloudcontroller.service.PolicyTemplateManageService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.Policy;
import vip.xiaonuo.biz.modular.cloudcontroller.mapper.PolicyMapper;
import vip.xiaonuo.biz.modular.cloudcontroller.service.PolicyService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略Service接口实现类
 *
 * <AUTHOR>
 * @date 2023/10/10 16:46
 **/
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_CLOUD)
@Slf4j
public class PolicyServiceImpl extends ServiceImpl<PolicyMapper, Policy> implements PolicyService {

    @Autowired
    private PolicyLimitService policyLimitService;

    @Resource
    private PolicyTemplateManageService policyTemplateManageService;

    @Override
    public Page<Policy> page(PolicyPageParam policyPageParam) {
        QueryWrapper<Policy> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(policyPageParam.getPolicyName())) {
            queryWrapper.lambda().like(Policy::getPolicyName, policyPageParam.getPolicyName());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getPolicyClassId())) {
            queryWrapper.lambda().eq(Policy::getPolicyClassId, policyPageParam.getPolicyClassId());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getStatus())) {
            queryWrapper.lambda().eq(Policy::getStatus, policyPageParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getDataType())) {
            queryWrapper.lambda().eq(Policy::getDataType, policyPageParam.getDataType());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getAppId())) {
            queryWrapper.lambda().eq(Policy::getAppId, policyPageParam.getAppId());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getSdkVersion())) {
            queryWrapper.lambda().eq(Policy::getSdkVersion, policyPageParam.getSdkVersion());
        }
        if (ObjectUtil.isAllNotEmpty(policyPageParam.getSortField(), policyPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(policyPageParam.getSortOrder());
            queryWrapper.orderBy(true, policyPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(policyPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(Policy::getCreateTime).orderByDesc(Policy::getUpdateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<Policy> listAll() {
        QueryWrapper<Policy> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(Policy::getPolicyId, Policy::getPolicyName, Policy::getDataType, Policy::getDim, Policy::getAppId, Policy::getSdkVersion, Policy::getPolicyTaskName, Policy::getPolicyTaskField, Policy::getPolicyTaskValue, Policy::getPolicyClassId, Policy::getRuleConditionDrl, Policy::getDescription, Policy::getCreateUser, Policy::getCreateTime, Policy::getUpdateUser, Policy::getUpdateTime, Policy::getStatus);
        queryWrapper.lambda().orderByDesc(Policy::getCreateTime).orderByDesc(Policy::getUpdateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<Policy> list(PolicyPageParam policyPageParam) {
        QueryWrapper<Policy> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(policyPageParam.getPolicyName())) {
            queryWrapper.lambda().like(Policy::getPolicyName, policyPageParam.getPolicyName());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getPolicyClassId())) {
            queryWrapper.lambda().eq(Policy::getPolicyClassId, policyPageParam.getPolicyClassId());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getStatus())) {
            queryWrapper.lambda().eq(Policy::getStatus, policyPageParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getDataType())) {
            queryWrapper.lambda().eq(Policy::getDataType, policyPageParam.getDataType());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getAppId())) {
            queryWrapper.lambda().eq(Policy::getAppId, policyPageParam.getAppId());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getSdkVersion())) {
            queryWrapper.lambda().eq(Policy::getSdkVersion, policyPageParam.getSdkVersion());
        }
        if (ObjectUtil.isAllNotEmpty(policyPageParam.getSortField(), policyPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(policyPageParam.getSortOrder());
            queryWrapper.orderBy(true, policyPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(policyPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(Policy::getCreateTime).orderByDesc(Policy::getUpdateTime);
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<Policy> policyOptions(PolicyPageParam policyPageParam) {
        QueryWrapper<Policy> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(policyPageParam.getPolicyName())) {
            queryWrapper.lambda().like(Policy::getPolicyName, policyPageParam.getPolicyName());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getPolicyClassId())) {
            queryWrapper.lambda().eq(Policy::getPolicyClassId, policyPageParam.getPolicyClassId());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getStatus())) {
            queryWrapper.lambda().eq(Policy::getStatus, policyPageParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getDataType())) {
            queryWrapper.lambda().eq(Policy::getDataType, policyPageParam.getDataType());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getAppId())) {
            queryWrapper.lambda().eq(Policy::getAppId, policyPageParam.getAppId());
        }
        if (ObjectUtil.isNotEmpty(policyPageParam.getSdkVersion())) {
            queryWrapper.lambda().eq(Policy::getSdkVersion, policyPageParam.getSdkVersion());
        }
        if (ObjectUtil.isAllNotEmpty(policyPageParam.getSortField(), policyPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(policyPageParam.getSortOrder());
            queryWrapper.orderBy(true, policyPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(policyPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(Policy::getCreateTime).orderByDesc(Policy::getUpdateTime);
        }
        // 只查询策略限制中未配置限制的策略可选
        List<PolicyLimit> policyLimitList = policyLimitService.list();
        if (CollectionUtils.isNotEmpty(policyLimitList)) {
            queryWrapper.lambda().notIn(Policy::getPolicyId, policyLimitList.stream().map(PolicyLimit::getPolicyId).collect(Collectors.toList()));
        }
        return this.list(queryWrapper);
    }

    @Override
    public void add(PolicyAddParam policyAddParam) {
        Policy policy = BeanUtil.toBean(policyAddParam, Policy.class);
        // 状态，-1删除，0无效，1有效
        if (policy.getStatus() == null) {
            policy.setStatus("1");
        }
        this.save(policy);
        // 策略限制
        if (policy.getPolicyId() != null && policyAddParam.getDeviceMaxNum() != null && policyAddParam.getDeviceMaxNum() > 0) {
            Integer deviceMaxNum = 30;
            if (policyAddParam.getDeviceMaxNum() != null) {
                deviceMaxNum = policyAddParam.getDeviceMaxNum();
            }
            PolicyLimitAddParam policyLimitAddParam = new PolicyLimitAddParam();
            policyLimitAddParam.setPolicyId(policy.getPolicyId());
            policyLimitAddParam.setDeviceMaxNum(deviceMaxNum);
            policyLimitService.add(policyLimitAddParam);
        }
        // 判断是否立即下发
        this.nowSend(policy);
    }

    /**
     * 立即下发策略
     *
     * @param policy 策略信息
     */
    private void nowSend(Policy policy) {
        if (StringUtils.isNotBlank(policy.getRuleConditionJson())) {
            JSONObject ruleConditionJson = JSONObject.parseObject(policy.getRuleConditionJson());
            JSONObject dataJson = ruleConditionJson.getJSONObject("dataJson");
            // 设备ID
            List<String> deviceIdList = new ArrayList<>();
            if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.getJSONArray("deviceIds"))) {
                JSONArray deviceIdsJSONArray = dataJson.getJSONArray("deviceIds");
                for (int i = 0; i < CollectionUtils.size(deviceIdsJSONArray); i++) {
                    String deviceId = deviceIdsJSONArray.getJSONObject(i).getString("deviceId");
                    CollectionUtils.addIgnoreNull(deviceIdList, deviceId);
                }
            }
            // 参数
            Map<String, List<String>> paramsOfLuaTaskMap = new LinkedHashMap<>();
            if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.getJSONArray("paramsOfLuaTask"))) {
                JSONArray paramsOfLuaTaskJSONArray = dataJson.getJSONArray("paramsOfLuaTask");
                for (int i = 0; i < CollectionUtils.size(paramsOfLuaTaskJSONArray); i++) {
                    JSONObject paramsOfLuaTask = paramsOfLuaTaskJSONArray.getJSONObject(i);
                    String luaTask = paramsOfLuaTask.getString("luaTask");
                    String param = paramsOfLuaTask.getString("param");
                    List<String> params = paramsOfLuaTaskMap.getOrDefault(luaTask, new ArrayList<>());
                    if (!params.contains(param)) {
                        // TODO 解决参数转义问题？
                        param = StringEscapeUtils.unescapeJson(param);
                        params.add(param);
                        paramsOfLuaTaskMap.put(luaTask, params);
                    }
                }
            }

            // 扩展参数
            Map<String, List<Map<String, String>>> extendedParamsOfLuaTaskMap = new LinkedHashMap<>();
            if (dataJson != null && CollectionUtils.isNotEmpty(dataJson.getJSONArray("extendedParams"))) {
                JSONArray extendedParamsJSONArray = dataJson.getJSONArray("extendedParams");
                // TODO 注意！注意！此处luaTask目前为固定指定为copy_files，因为目前只有copy_files任务存在扩展参数，若后期多个任务均存在扩展参数，则此处必须调整为从前端数据中获取luaTask
                String luaTask = "copy_files";
                List<Map<String, String>> extendedParamsOfLuaTaskList = new ArrayList<>();
                for (int i = 0; i < CollectionUtils.size(extendedParamsJSONArray); i++) {
                    JSONObject extendedParams = extendedParamsJSONArray.getJSONObject(i);
                    if (extendedParams != null && CollectionUtils.isNotEmpty(extendedParams.keySet())) {
                        Map<String, String> paramMap = new HashMap<>();
                        for (String key : extendedParams.keySet()) {
                            // key为低代码框架自动生成的，非真实业务字段，需过滤
                            if (!StringUtils.equalsIgnoreCase(key, "key")) {
//                                paramMap.put(key, extendedParams.getString(key));
                                // TODO 解决参数转义问题
                                paramMap.put(StringEscapeUtils.unescapeJson(key), StringEscapeUtils.unescapeJson(extendedParams.getString(key)));
                            }
                        }
                        if (MapUtils.isNotEmpty(paramMap)) {
                            CollectionUtils.addIgnoreNull(extendedParamsOfLuaTaskList, paramMap);
                        }
                    }
                }
                extendedParamsOfLuaTaskMap.put(luaTask, extendedParamsOfLuaTaskList);
            }

            // 所有设备立即下发策略
            if (CollectionUtils.isNotEmpty(deviceIdList)) {
                // 规则匹配结果数据
                RuleMatchResultDTO ruleMatchResultDTO = new RuleMatchResultDTO();
                ruleMatchResultDTO.setPolicyId(policy.getPolicyId());
                ruleMatchResultDTO.setParamsOfLuaTaskMap(paramsOfLuaTaskMap);
                ruleMatchResultDTO.setExtendedParamsOfLuaTaskMap(extendedParamsOfLuaTaskMap);
                ruleMatchResultDTO.setFired(Boolean.TRUE);
                for (String deviceId : deviceIdList) {
                    DataMatchResultDTO dataMatchResultDTO = new DataMatchResultDTO();
                    dataMatchResultDTO.setEventTime(DateUtil.currentSeconds());
                    dataMatchResultDTO.setDeviceId(deviceId);
                    dataMatchResultDTO.setAppId(policy.getAppId());
                    dataMatchResultDTO.setSdkVersion(policy.getSdkVersion());
                    dataMatchResultDTO.setLastUpdateTime(DateUtil.current());
                    List<RuleMatchResultDTO> ruleMatchResultDTOList = new ArrayList<>();
                    ruleMatchResultDTOList.add(ruleMatchResultDTO);
                    dataMatchResultDTO.setRuleMatchResultDTOList(ruleMatchResultDTOList);
                    policyTemplateManageService.update(dataMatchResultDTO);
                }
                // 更新策略状态
                policy.setStatus("-1");
                policy.setRuleConditionDrl(null);
                this.updateById(policy);
            }
        }
    }

    @Override
    public void edit(PolicyEditParam policyEditParam) {
        Policy policy = this.queryEntity(policyEditParam.getPolicyId());
        BeanUtil.copyProperties(policyEditParam, policy);
        this.updateById(policy);
        // 判断是否立即下发
        this.nowSend(policy);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<PolicyIdParam> policyIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(policyIdParamList, PolicyIdParam::getPolicyId));
    }

    @Override
    public Policy detail(PolicyIdParam policyIdParam) {
        return this.queryEntity(policyIdParam.getPolicyId());
    }

    @Override
    public Policy queryEntity(Integer id) {
        Policy policy = this.getById(id);
        if (ObjectUtil.isEmpty(policy)) {
            throw new CommonException("策略不存在，id值为：{}", id);
        }
        return policy;
    }
}
