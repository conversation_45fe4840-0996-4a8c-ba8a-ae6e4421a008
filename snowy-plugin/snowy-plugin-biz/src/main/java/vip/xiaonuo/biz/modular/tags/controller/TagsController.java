package vip.xiaonuo.biz.modular.tags.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.biz.modular.remote.service.TagsRemoteService;
import vip.xiaonuo.biz.modular.tags.service.TagsService;
import vip.xiaonuo.common.enums.CommonExceptionEnum;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Api(tags = "风控标签查询接口")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@RequestMapping("/biz/tags")
@Validated
public class TagsController {

    @Autowired
    private TagsRemoteService tagsRemoteService;

    @Autowired
    private TagsService tagsService;

    @ApiOperationSupport(order = 1)
    @ApiOperation("单用户查询")
    @PostMapping("querySingle")
    public CommonResult<JSONObject> querySingle(@RequestBody @Valid JSONObject singleUserRequest) {
        return CommonResult.data(tagsRemoteService.querySingle(singleUserRequest));
    }

    @ApiOperationSupport(order = 2)
    @ApiOperation("用户群查询")
    @PostMapping("queryGroup")
    public CommonResult<JSONObject> queryGroup(@RequestBody @Valid JSONObject userBaseRequest) {
        return CommonResult.data(tagsRemoteService.queryGroup(userBaseRequest));
    }

    @ApiOperationSupport(order = 3)
    @ApiOperation("查看所有的Tag与所属的Domain信息")
    @GetMapping("tag")
    public CommonResult<JSONObject> tag() {
        return CommonResult.data(tagsRemoteService.tag());
    }

    @ApiOperationSupport(order = 4)
    @ApiOperation("通过namespace名称查看所属的所有Tag")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "domainName", value = "domainName", paramType = "query", required = true, dataType = "String")})
    @GetMapping("tagNs")
    public CommonResult<JSONObject> tagNs(@RequestParam(value = "domainName", required = true) String domainName) {
        return CommonResult.data(tagsRemoteService.tagNs(domainName));
    }

    @ApiOperationSupport(order = 5)
    @ApiOperation("查看ns")
    @GetMapping("ns")
    public CommonResult<JSONObject> ns() {
        return CommonResult.data(tagsRemoteService.ns());
    }


    @ApiOperation("批量查询用户标签")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uids", value = "uids", paramType = "query", required = true, dataType = "String")})
    @GetMapping("listTags")
    public CommonResult<Map<String, List<String>>> listTags(@RequestParam(value = "uids", required = true) String uids) {
        String userIds = StringUtils.deleteWhitespace(uids);
        if (StringUtils.isBlank(userIds)) {
            return new CommonResult<Map<String, List<String>>>().setCode(CommonExceptionEnum.ERROR415.getCode()).setMsg("需要查询的用户ID不能为空!");
        }
        List<String> userIdList = Arrays.asList(userIds.split(","));
        return CommonResult.data(tagsService.listTags(userIdList));
    }
}
