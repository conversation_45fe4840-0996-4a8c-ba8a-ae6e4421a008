package vip.xiaonuo.biz.modular.cloud.controller;

import cn.dev33.satoken.annotation.SaCheckBasic;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.cloud.dto.DataMatchResultDTO;
import vip.xiaonuo.biz.modular.cloud.service.MpsPolicyTemplateManageService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/4/12 17:20
 */
@Api(tags = "安全策略下发联动-策略模板管理器(Http Basic 认证)")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
@SaCheckBasic
@RequestMapping("biz/cloud/mps/policyTemplateManage")
public class MpsPolicyTemplateManageController {
    @Resource
    private MpsPolicyTemplateManageService mpsPolicyTemplateManageService;

    /**
     * 编辑策略
     *
     * <AUTHOR>
     * @date 2023/10/10 16:46
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("更新策略模板")
    @PostMapping("update")
    public CommonResult<String> update(@RequestBody @Valid DataMatchResultDTO dataMatchResultDTO) {
        mpsPolicyTemplateManageService.update(dataMatchResultDTO);
        return CommonResult.ok();
    }
}
