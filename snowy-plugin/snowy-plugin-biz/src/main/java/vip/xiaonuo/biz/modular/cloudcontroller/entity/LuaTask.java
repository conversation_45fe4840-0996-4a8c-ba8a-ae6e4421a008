/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.cloudcontroller.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 安全策略下发联动-LuaTask表实体
 *
 * <AUTHOR>
 * @date 2023/10/10 14:32
 **/
@Getter
@Setter
@TableName("tb_cloud_controller_lua_task")
public class LuaTask {

    /**
     * LuaTask ID
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "LuaTask ID", position = 1)
    private String luaTaskId;

    /**
     * LuaTask名称
     */
    @ApiModelProperty(value = "LuaTask名称", position = 2)
    private String luaTaskNameCn;

    /**
     * LuaTask英文名
     */
    @ApiModelProperty(value = "LuaTask英文名", position = 3)
    private String luaTaskNameEn;

    /**
     * 是否有参数
     */
    @ApiModelProperty(value = "是否有参数", position = 4)
    private String hasParams;

    /**
     * 是否有扩展参数，YES 有，NO 无
     */
    @ApiModelProperty(value = "是否有扩展参数，YES 有，NO 无", position = 4)
    private String hasExtendedParams;

    /**
     * 是否强制执行，YES 强制执行，NO 非强制执行
     */
    @ApiModelProperty(value = "是否强制执行，YES 强制执行，NO 非强制执行", position = 4)
    private String forceExecution;

    /**
     * 任务类型，=lua Lua任务，=native Native任务
     */
    @ApiModelProperty(value = "任务类型，=lua Lua任务，=native Native任务", position = 4)
    private String taskType;

    /**
     * 应用ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "应用ID", position = 5)
    private Integer appId;

    /**
     * SDK版本号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "SDK版本号", position = 5)
    private String sdkVersion;

    /**
     * 参数模板
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "参数模板", position = 5)
    private String paramsTemplate;

    /**
     * luaCode
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "luaCode", position = 6)
    private String luaCode;

    /**
     * 描述
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "描述", position = 6)
    private String description;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", position = 7)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 8)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人", position = 9)
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间", position = 10)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", position = 11)
    private String status;
}
