package vip.xiaonuo.biz.modular.cloud.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 规则匹配结果数据结构
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/10/11 16:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RuleMatchResultDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 规则
     */
//    private RuleEntity ruleEntity;

    /**
     * 策略ID
     */
    private Integer policyId;

    /**
     * 是否触发
     */
    private Boolean fired;

    /**
     * tagId
     */
    private String tagId;

    /**
     * level
     */
    private Integer level;

    /**
     * 扩展数据
     */
    private Map<String, Object> extDataMap;
}
