package vip.xiaonuo.biz.modular.data.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.core.constant.CommonConstant;
import vip.xiaonuo.biz.modular.data.dto.TagsCntExcelDTO;
import vip.xiaonuo.biz.modular.data.dto.UserMatchStatExcelDTO;
import vip.xiaonuo.biz.modular.monitor.entity.MonitorGameDay;
import vip.xiaonuo.biz.modular.remote.service.DataRemoteService;
import vip.xiaonuo.biz.modular.remote.service.TagsRemoteService;
import vip.xiaonuo.biz.modular.syncInfo.entity.TagInfo;
import vip.xiaonuo.biz.modular.syncInfo.param.TagInfoPageParam;
import vip.xiaonuo.biz.modular.syncInfo.service.TagInfoService;
import vip.xiaonuo.common.enums.CommonExceptionEnum;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/9/5 10:31
 */
@Api(tags = "Impala数据查询")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
@Slf4j
@RequestMapping("biz/data/impala")
public class ImpalaController {

    @Autowired
    private DataRemoteService dataRemoteService;

    @Autowired
    private TagsRemoteService tagsRemoteService;

    @Autowired
    private TagInfoService tagInfoService;

    /**
     * 获取用户参赛情况(每个用户)
     *
     * @param startDay     开始日期
     * @param endDay       结束日期
     * @param tagNameEn    标签英文名
     * @param dataType     标签类型
     * @param defaultValue 标签默认值
     * @param mpId         比赛产品ID
     * @return 用户参赛情况(每个用户)
     */
    @ApiOperation(value = "下载用户参赛情况(每个用户)", notes = "下载用户参赛情况(每个用户)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDay", value = "开始日期，格式yyyy-MM-dd,默认昨天", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "endDay", value = "结束日期，格式yyyy-MM-dd,默认昨天", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "tagNameEn", value = "标签英文名", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dataType", value = "标签类型", paramType = "query", required = true, defaultValue = "INT", dataType = "String"),
            @ApiImplicitParam(name = "defaultValue", value = "标签默认值", paramType = "query", required = true, defaultValue = "0", dataType = "String"),
            @ApiImplicitParam(name = "mpId", value = "比赛产品ID", paramType = "query", required = true, dataType = "LONG")
    })
    @GetMapping(value = "downloadUserMatchStatGroupByUid", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadUserMatchStatGroupByUid(
            @RequestParam(value = "startDay", required = false) String startDay
            , @RequestParam(value = "endDay", required = false) String endDay
            , @RequestParam(value = "tagNameEn", required = true) String tagNameEn
            , @RequestParam(value = "dataType", required = true) String dataType
            , @RequestParam(value = "defaultValue", required = true) String defaultValue
            , @RequestParam(value = "mpId", required = true) Long mpId
            , HttpServletResponse response) throws IOException {
        if (StringUtils.isBlank(tagNameEn) || StringUtils.isBlank(dataType) || StringUtils.isBlank(defaultValue) || mpId == null) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR415.getCode(), "请输入标签英文名、标签类型、标签默认值和比赛产品ID!", null)));
            return;
        }
        List<UserMatchStatExcelDTO> userMatchStatExcelDTOList = new ArrayList<>();
        // 获取时间区间日期
        Date start = StringUtils.isBlank(startDay) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(startDay)).toJdkDate();
        Date end = StringUtils.isBlank(endDay) ? DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.endOfDay(DateUtil.parse(endDay)).toJdkDate();
        List<DateTime> dateTimeList = DateUtil.rangeToList(start, end, DateField.DAY_OF_YEAR);
        for (DateTime dateTime : dateTimeList) {
            String day = DateUtil.format(dateTime, DatePattern.NORM_DATE_PATTERN);
            LocalDateTime startTime = LocalDateTime.now();
            // 自定义超时时间
            Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 30);
            CommonResult<List<MonitorGameDay>> result = dataRemoteService.listUserMatchStatGroupByUid(options, day, tagNameEn, dataType, defaultValue, mpId);
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            if (result == null || !CommonExceptionEnum.OK200.getCode().equals(result.getCode())) {
                log.warn("远程调用获取用户参赛情况(每个用户)失败!远程返回结果={},day={},tagNameEn={},dataType={},defaultValue={},mpId={}", JSONObject.toJSONString(result), day, tagNameEn, dataType, defaultValue, mpId);
                continue;
            }
            // 空白数据
            if (CollUtil.isEmpty(result.getData())) {
                log.warn("远程调用获取用户参赛情况(每个用户)结果为空!远程返回结果={},day={},tagNameEn={},dataType={},defaultValue={},mpId={}", JSONObject.toJSONString(result), day, tagNameEn, dataType, defaultValue, mpId);
            }
            log.info("远程调用获取用户参赛情况返回[{}]条数据,耗时=[{}]ms,day={},tagNameEn={},dataType={},defaultValue={},mpId={}", CollUtil.size(result.getData()), duration.toMillis(), day, tagNameEn, dataType, defaultValue, mpId);
            if (CollUtil.isNotEmpty(result.getData())) {
                for (MonitorGameDay monitorGameDay : result.getData()) {
                    UserMatchStatExcelDTO userMatchStatExcelDTO = new UserMatchStatExcelDTO();
                    BeanUtils.copyProperties(monitorGameDay, userMatchStatExcelDTO);
                    userMatchStatExcelDTO.setCreateTime(new Date());
                    CollectionUtils.addIgnoreNull(userMatchStatExcelDTOList, userMatchStatExcelDTO);
                }
            }
        }

        // 生成文件路径
        String fileName = "tag_user_match_stat_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ExcelTypeEnum.XLSX.getValue();
        // 写到不同的sheet 不同的对象
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
//            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);

            // 此处需要设置不关闭流
            excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 分别写入不同的sheet
            // 去调用写入,这里我调用了五次，实际使用时根据数据库分页的总的页数来。这里最终会写到5个sheet里面
            // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样。这里注意DemoData.class 可以每次都变，我这里为了方便 所以用的同一个class 实际上可以一直变
            WriteSheet userMatchStatlWriteSheet = EasyExcel.writerSheet(0, "异常用户参赛情况").head(UserMatchStatExcelDTO.class).build();
            excelWriter.write(userMatchStatExcelDTOList, userMatchStatlWriteSheet);
        } catch (Exception e) {
            log.error("生成文件异常!", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR500.getCode(), "下载文件失败，请稍后重试!", null)));
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 获取用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况,全用户)
     *
     * @param day          日期
     * @param tagNameEn    标签英文名
     * @param dataType     标签类型
     * @param defaultValue 标签默认值
     * @param mpId         比赛产品ID
     * @return 用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况, 全用户)
     */
    @ApiOperation(value = "下载用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况,全用户)", notes = "下载用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况,全用户)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "day", value = "日期，格式yyyy-MM-dd", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "tagNameEn", value = "标签英文名", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "dataType", value = "标签类型", paramType = "query", required = true, defaultValue = "INT", dataType = "String"),
            @ApiImplicitParam(name = "defaultValue", value = "标签默认值", paramType = "query", required = true, defaultValue = "0", dataType = "String"),
            @ApiImplicitParam(name = "mpId", value = "比赛产品ID", paramType = "query", required = true, dataType = "LONG")
    })
    @GetMapping(value = "downloadUserMatchStatInMpId", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadUserMatchStatInMpId(
            @RequestParam(value = "day", required = false) String day
            , @RequestParam(value = "tagNameEn", required = true) String tagNameEn
            , @RequestParam(value = "dataType", required = true) String dataType
            , @RequestParam(value = "defaultValue", required = true) String defaultValue
            , @RequestParam(value = "mpId", required = true) Long mpId
            , HttpServletResponse response) throws IOException {
        if (StringUtils.isBlank(tagNameEn) || StringUtils.isBlank(dataType) || StringUtils.isBlank(defaultValue) || mpId == null) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR415.getCode(), "请输入标签英文名、标签类型、标签默认值和比赛产品ID!", null)));
            return;
        }
        List<UserMatchStatExcelDTO> userMatchStatExcelDTOList = new ArrayList<>();

        LocalDateTime startTime = LocalDateTime.now();
        // 自定义超时时间
        Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 30);
        CommonResult<List<MonitorGameDay>> result = dataRemoteService.listUserMatchStatInMpId(options, day, tagNameEn, dataType, defaultValue, mpId);
        LocalDateTime endTime = LocalDateTime.now();
        Duration duration = Duration.between(startTime, endTime);
        if (result == null || !CommonExceptionEnum.OK200.getCode().equals(result.getCode())) {
            log.warn("远程调用获取用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况,全用户)失败!远程返回结果={},day={},tagNameEn={},dataType={},defaultValue={},mpId={}", JSONObject.toJSONString(result), day, tagNameEn, dataType, defaultValue, mpId);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR500.getCode(), "远程调用获取用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况,全用户)失败!", null)));
            return;
        }
        // 空白数据
        if (CollUtil.isEmpty(result.getData())) {
            log.warn("远程调用获取用户参赛情况(指定标签在指定比赛产品ID中触发用户在所有比赛中的参赛情况,全用户)结果为空!远程返回结果={},day={},tagNameEn={},dataType={},defaultValue={},mpId={}", JSONObject.toJSONString(result), day, tagNameEn, dataType, defaultValue, mpId);
        }
        log.info("远程调用获取用户参赛情况返回[{}]条数据,耗时=[{}]ms,day={},tagNameEn={},dataType={},defaultValue={},mpId={}", CollUtil.size(result.getData()), duration.toMillis(), day, tagNameEn, dataType, defaultValue, mpId);

        if (CollUtil.isNotEmpty(result.getData())) {
            for (MonitorGameDay monitorGameDay : result.getData()) {
                UserMatchStatExcelDTO userMatchStatExcelDTO = new UserMatchStatExcelDTO();
                BeanUtils.copyProperties(monitorGameDay, userMatchStatExcelDTO);
                userMatchStatExcelDTO.setCreateTime(new Date());
                CollectionUtils.addIgnoreNull(userMatchStatExcelDTOList, userMatchStatExcelDTO);
            }
        }

        // 生成文件路径
        String fileName = "inmapid_user_match_stat_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ExcelTypeEnum.XLSX.getValue();
        // 写到不同的sheet 不同的对象
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
//            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);

            // 此处需要设置不关闭流
            excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 分别写入不同的sheet
            // 去调用写入,这里我调用了五次，实际使用时根据数据库分页的总的页数来。这里最终会写到5个sheet里面
            // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样。这里注意DemoData.class 可以每次都变，我这里为了方便 所以用的同一个class 实际上可以一直变
            WriteSheet userMatchStatlWriteSheet = EasyExcel.writerSheet(0, "异常用户参赛情况").head(UserMatchStatExcelDTO.class).build();
            excelWriter.write(userMatchStatExcelDTOList, userMatchStatlWriteSheet);
        } catch (Exception e) {
            log.error("生成文件异常!", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR500.getCode(), "下载文件失败，请稍后重试!", null)));
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    /**
     * 根据用户ID统计标签聚集性
     *
     * @param uids 用户ID
     * @return 标签聚集性
     */
    @ApiOperation(value = "用户群标签聚集性", notes = "根据用户ID统计标签聚集性")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uids", value = "用户ID(多个使用逗号\",\"分隔)", paramType = "query", required = true, dataType = "String")
    })
    @GetMapping(value = "downloadTagsCntByUids", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadTagsCntByUids(
            @RequestParam(value = "uids", required = true) String uids
            , HttpServletResponse response) throws IOException {
        uids = StringUtils.deleteWhitespace(uids);
        if (StringUtils.isBlank(uids)) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR415.getCode(), "查询的用户ID不能为空!", null)));
            return;
        }

        String domainNameEn = "";
        domainNameEn = StringUtils.defaultIfBlank(domainNameEn, CommonConstant.DEFAULT_DOMAIN_NAME_EN);
        JSONObject jsonObject = tagsRemoteService.tagNs(domainNameEn);
        if (jsonObject == null || !CommonExceptionEnum.OK200.getCode().equals(jsonObject.getInteger("code")) || jsonObject.get("data") == null) {
            log.warn("远程调用通过namespace名称查看所属的所有Tag失败!远程返回结果={},domainNameEn={}", JSONObject.toJSONString(jsonObject), domainNameEn);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR500.getCode(), "远程调用通过namespace名称查看所属的所有Tag失败!", null)));
            return;
        }

        // 标签信息列表
        List<TagInfo> tagInfoEntityList = new ArrayList<>();
        // 表格head
        List<List<String>> excelHead = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("pt_dt");
        excelHead.add(head0);
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        for (int i = 0; i < jsonArray.size(); i++) {
            // 标签信息列表
            JSONObject tagJSONObject = jsonArray.getJSONObject(i);
            String tagNameEn = tagJSONObject.getString("tagName");
            String dataType = tagJSONObject.getString("dataType");
            String defaultValue = tagJSONObject.getString("defaultValue");
            String tagNameZh = tagJSONObject.getString("tagExplain");
            TagInfo tagInfo = new TagInfo();
            tagInfo.setTagNameEn(tagNameEn);
            tagInfo.setDataType(dataType);
            tagInfo.setDefaultValue(defaultValue);
            CollectionUtils.addIgnoreNull(tagInfoEntityList, tagInfo);
            // 表格head信息
            List<String> headTmp = new ArrayList<>();
            headTmp.add(tagNameEn);
            headTmp.add(tagNameZh);
            excelHead.add(headTmp);
        }


//        List<UserMatchStatExcelDTO> userMatchStatExcelDTOList = new ArrayList<>();

        LocalDateTime startTime = LocalDateTime.now();
        // 自定义超时时间
        Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 30);
        CommonResult<List<LinkedHashMap<String, Object>>> result = dataRemoteService.listTagsCntByUids(options, uids, tagInfoEntityList);
        LocalDateTime endTime = LocalDateTime.now();
        Duration duration = Duration.between(startTime, endTime);
        if (result == null || !CommonExceptionEnum.OK200.getCode().equals(result.getCode())) {
//            log.warn("远程调用根据用户ID统计标签聚集性失败!远程返回结果={},day={},tagNameEn={},dataType={},defaultValue={},mpId={}", JSONObject.toJSONString(result), day, tagNameEn, dataType, defaultValue, mpId);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR500.getCode(), "远程调用根据用户ID统计标签聚集性失败!", null)));
            return;
        }
        // 空白数据
        if (CollUtil.isEmpty(result.getData())) {
//            log.warn("远程调用获取用户参赛情况(每个用户)结果为空!远程返回结果={},day={},tagNameEn={},dataType={},defaultValue={},mpId={}", JSONObject.toJSONString(result), day, tagNameEn, dataType, defaultValue, mpId);
        }
        log.info("远程调用根据用户ID统计标签聚集性返回[{}]条数据,耗时=[{}]ms", CollUtil.size(result.getData()), duration.toMillis());

        // 2.标签别名
        Map<String, TagInfo> tagInfoMap = new HashMap<>();
        List<TagInfo> tagInfoList = tagInfoService.list(new TagInfoPageParam());
        if (CollectionUtils.isNotEmpty(tagInfoList)) {
            for (TagInfo tagInfo : tagInfoList) {
                if (tagInfoMap.get(tagInfo.getTagNameEn()) == null || StringUtils.isBlank(tagInfoMap.get(tagInfo.getTagNameEn()).getTagNameAlias())) {
                    tagInfoMap.put(tagInfo.getTagNameEn(), tagInfo);
                }
            }
        }

        // 数据列表(宽表)
        List<List<Object>> excelDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(result.getData())) {
            for (LinkedHashMap<String, Object> line : result.getData()) {
                List<Object> dataLine = new ArrayList<>();
                for (List<String> headTmp : excelHead) {
                    dataLine.add(line.get(headTmp.get(0)));
                }
                excelDataList.add(dataLine);
            }
        }
        // 数据列表(窄表)
        List<TagsCntExcelDTO> tagsCntExcelDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(result.getData())) {
            for (LinkedHashMap<String, Object> line : result.getData()) {
                String pt_dt = String.valueOf(line.get("pt_dt"));
                for (String key : line.keySet()) {
                    if (!StringUtils.equalsIgnoreCase(key, "pt_dt")) {
                        String tagExplain = null;
                        // 优先使用标签别名
                        TagInfo tagInfo = tagInfoMap.get(key);
                        if (tagInfo != null) {
                            tagExplain = StringUtils.firstNonBlank(tagInfo.getTagNameAlias(), tagInfo.getTagNameZh());
                        }
                        if (StringUtils.isBlank(tagExplain)) {
                            tagExplain = key;
                        }
                        TagsCntExcelDTO tagsCntExcelDTO = new TagsCntExcelDTO();
                        tagsCntExcelDTO.setDay(pt_dt);
                        tagsCntExcelDTO.setTagNameEn(key);
                        tagsCntExcelDTO.setTagNameZh(tagExplain);
                        tagsCntExcelDTO.setUidCnt(Long.valueOf(String.valueOf(line.get(key))));
                        CollectionUtils.addIgnoreNull(tagsCntExcelDTOList, tagsCntExcelDTO);
                    }
                }
            }
        }

        // 生成文件路径
        String fileName = "tag_cnt_" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + ExcelTypeEnum.XLSX.getValue();
        // 写到不同的sheet 不同的对象
        ExcelWriter excelWriter = null;
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
//            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);

            // 此处需要设置不关闭流
            excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 分别写入不同的sheet
            // 去调用写入,这里我调用了五次，实际使用时根据数据库分页的总的页数来。这里最终会写到5个sheet里面
            // 每次都要创建writeSheet 这里注意必须指定sheetNo 而且sheetName必须不一样。这里注意DemoData.class 可以每次都变，我这里为了方便 所以用的同一个class 实际上可以一直变
            WriteSheet userMatchStatlWriteSheet = EasyExcel.writerSheet(1, "用户群标签聚集性(宽表)").head(excelHead).build();
            excelWriter.write(excelDataList, userMatchStatlWriteSheet);

            excelWriter.write(tagsCntExcelDTOList, EasyExcel.writerSheet(0, "用户群标签聚集性(窄表)").head(TagsCntExcelDTO.class).build());
        } catch (Exception e) {
            log.error("生成文件异常!", e);
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(CommonResult.get(CommonExceptionEnum.ERROR500.getCode(), "下载文件失败，请稍后重试!", null)));
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }
}
