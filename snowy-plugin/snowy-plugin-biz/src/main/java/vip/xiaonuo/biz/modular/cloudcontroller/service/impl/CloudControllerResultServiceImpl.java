/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.cloudcontroller.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.PolicyResultTreeDTO;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.CloudControllerResult;
import vip.xiaonuo.biz.modular.cloudcontroller.mapper.CloudControllerResultMapper;
import vip.xiaonuo.biz.modular.cloudcontroller.param.CloudControllerResultAddParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.CloudControllerResultEditParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.CloudControllerResultIdParam;
import vip.xiaonuo.biz.modular.cloudcontroller.param.CloudControllerResultPageParam;
import vip.xiaonuo.biz.modular.cloudcontroller.service.CloudControllerResultService;

import java.io.File;
import java.util.*;

/**
 * 回传结果Service接口实现类
 *
 * <AUTHOR>
 * @date 2023/11/10 14:28
 **/
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_CLOUD)
@Slf4j
@Service
public class CloudControllerResultServiceImpl extends ServiceImpl<CloudControllerResultMapper, CloudControllerResult> implements CloudControllerResultService {

    @Override
    public Page<CloudControllerResult> page(CloudControllerResultPageParam cloudControllerResultPageParam) {
        QueryWrapper<CloudControllerResult> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getLuaName())) {
            queryWrapper.lambda().eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getDeviceId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getDeviceId, cloudControllerResultPageParam.getDeviceId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getHostName())) {
            queryWrapper.lambda().like(CloudControllerResult::getHostName, cloudControllerResultPageParam.getHostName());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getPolicyId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getPolicyIds, cloudControllerResultPageParam.getPolicyId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getRecordId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getRecordIds, cloudControllerResultPageParam.getRecordId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getFileId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getFileId, cloudControllerResultPageParam.getFileId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getDataJson())) {
            queryWrapper.lambda().like(CloudControllerResult::getDataJson, cloudControllerResultPageParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getAbResult())) {
            queryWrapper.lambda().like(CloudControllerResult::getAbResult, "\"" + cloudControllerResultPageParam.getAbResult() + "\"");
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getAbContent())) {
            queryWrapper.lambda().like(CloudControllerResult::getAbContent, cloudControllerResultPageParam.getAbContent());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getOcrContent())) {
            queryWrapper.lambda().like(CloudControllerResult::getOcrContent, cloudControllerResultPageParam.getOcrContent());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getStatus())) {
            queryWrapper.lambda().eq(CloudControllerResult::getStatus, cloudControllerResultPageParam.getStatus());
        }
        if (ObjectUtil.isAllNotEmpty(cloudControllerResultPageParam.getSortField(), cloudControllerResultPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(cloudControllerResultPageParam.getSortOrder());
            queryWrapper.orderBy(true, cloudControllerResultPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(cloudControllerResultPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(CloudControllerResult::getCreateTime).orderByDesc(CloudControllerResult::getUpdateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public Page<CloudControllerResult> resultDataPage(CloudControllerResultPageParam cloudControllerResultPageParam) {
        String searchKey = cloudControllerResultPageParam.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {
            String policyIdStr = null;
            String deviceId = null;
            if (StringUtils.equalsIgnoreCase(cloudControllerResultPageParam.getCategory(), "POLICY")) {
                policyIdStr = StringUtils.substringBefore(searchKey, "_");
                deviceId = StringUtils.substringAfter(searchKey, "_");
            } else if (StringUtils.equalsIgnoreCase(cloudControllerResultPageParam.getCategory(), "DEVICE")) {
                policyIdStr = StringUtils.substringAfterLast(searchKey, "_");
                deviceId = StringUtils.substringBeforeLast(searchKey, "_");
            }
            Integer policyId = null;
            if (StringUtils.equalsIgnoreCase("-1", policyIdStr)) {
                policyId = -1;
            } else if (StringUtils.isNumeric(policyIdStr)) {
                try {
                    policyId = Integer.valueOf(policyIdStr);
                } catch (NumberFormatException e) {
                    log.warn("解析策略ID异常，policyIdStr={}", policyIdStr, e);
                }
            }
            if (policyId != null) {
                cloudControllerResultPageParam.setPolicyId(policyId);
            }
            if (StringUtils.isNotBlank(deviceId)) {
                cloudControllerResultPageParam.setDeviceId(deviceId);
            }

        }

        QueryWrapper<CloudControllerResult> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getLuaName())) {
            queryWrapper.lambda().eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getDeviceId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getDeviceId, cloudControllerResultPageParam.getDeviceId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getHostName())) {
            queryWrapper.lambda().like(CloudControllerResult::getHostName, cloudControllerResultPageParam.getHostName());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getFileId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getFileId, cloudControllerResultPageParam.getFileId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getDataJson())) {
            queryWrapper.lambda().like(CloudControllerResult::getDataJson, cloudControllerResultPageParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getAbResult())) {
            queryWrapper.lambda().like(CloudControllerResult::getAbResult, "\"" + cloudControllerResultPageParam.getAbResult() + "\"");
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getAbContent())) {
            queryWrapper.lambda().like(CloudControllerResult::getAbContent, cloudControllerResultPageParam.getAbContent());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getOcrContent())) {
            queryWrapper.lambda().like(CloudControllerResult::getOcrContent, cloudControllerResultPageParam.getOcrContent());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getStatus())) {
            queryWrapper.lambda().eq(CloudControllerResult::getStatus, cloudControllerResultPageParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getPolicyId())) {
            if (Integer.valueOf(-1).equals(cloudControllerResultPageParam.getPolicyId())) {
                queryWrapper.lambda().and(q -> q.isNull(CloudControllerResult::getPolicyIds).or().eq(CloudControllerResult::getPolicyIds, "").or().eq(CloudControllerResult::getPolicyIds, "[]"));
            } else {
                queryWrapper.lambda().inSql(CloudControllerResult::getResultId, String.format("select distinct result_id from tb_cloud_controller_policy_result_link where policy_id=%s", cloudControllerResultPageParam.getPolicyId()));
            }
        }
        if (ObjectUtil.isAllNotEmpty(cloudControllerResultPageParam.getSortField(), cloudControllerResultPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(cloudControllerResultPageParam.getSortOrder());
            queryWrapper.orderBy(true, cloudControllerResultPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(cloudControllerResultPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByDesc(CloudControllerResult::getCreateTime).orderByDesc(CloudControllerResult::getUpdateTime);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public String add(CloudControllerResultAddParam cloudControllerResultAddParam) {
        CloudControllerResult cloudControllerResult = BeanUtil.toBean(cloudControllerResultAddParam, CloudControllerResult.class);
        cloudControllerResult.setResultId(IdWorker.getTimeId());
        // 状态，-1删除，0无效，1有效
        if (cloudControllerResult.getStatus() == null) {
            cloudControllerResult.setStatus("1");
        }
        this.save(cloudControllerResult);
        return cloudControllerResult.getResultId();
    }

    @Override
    public void edit(CloudControllerResultEditParam cloudControllerResultEditParam) {
        CloudControllerResult cloudControllerResult = this.queryEntity(cloudControllerResultEditParam.getResultId());
        BeanUtil.copyProperties(cloudControllerResultEditParam, cloudControllerResult);
        this.updateById(cloudControllerResult);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<CloudControllerResultIdParam> cloudControllerResultIdParamList) {
        // 执行删除
        this.removeBatchByIds(CollStreamUtil.toList(cloudControllerResultIdParamList, CloudControllerResultIdParam::getResultId));
    }

    @Override
    public CloudControllerResult detail(CloudControllerResultIdParam cloudControllerResultIdParam) {
        return this.queryEntity(cloudControllerResultIdParam.getResultId());
    }

    @Override
    public CloudControllerResult queryEntity(String id) {
        CloudControllerResult cloudControllerResult = this.getById(id);
        if (ObjectUtil.isEmpty(cloudControllerResult)) {
            throw new CommonException("回传结果不存在，id值为：{}", id);
        }
        return cloudControllerResult;
    }

    @Override
    public Map<String, Object> screenshotContext(CloudControllerResultPageParam cloudControllerResultPageParam) {
        Map<String, Object> result = new HashMap<>();

        cloudControllerResultPageParam.setLuaName("create_screen_shot");

        String currentResultId = cloudControllerResultPageParam.getResultId();
        if (StringUtils.isBlank(currentResultId)) {
            result.put("currentRecord", null);
            result.put("previousRecord", null);
            result.put("nextRecord", null);
            result.put("totalCount", 0);
            result.put("currentIndex", -1);
            return result;
        }

        // 构建基础查询条件
        QueryWrapper<CloudControllerResult> baseQueryWrapper = new QueryWrapper<>();
        baseQueryWrapper.lambda()
                .eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName())
                .isNotNull(CloudControllerResult::getFileId)
                .ne(CloudControllerResult::getFileId, "");

        // 应用搜索条件
        applySearchConditions(baseQueryWrapper, cloudControllerResultPageParam);

        // 获取当前记录
        CloudControllerResult currentRecord = this.getById(currentResultId);
        if (currentRecord == null) {
            result.put("currentRecord", null);
            result.put("previousRecord", null);
            result.put("nextRecord", null);
            result.put("totalCount", 0);
            result.put("currentIndex", -1);
            return result;
        }

        // 获取总数
        long totalCount = this.count(baseQueryWrapper);

        // 获取当前记录的位置（通过复合排序：创建时间DESC + resultId DESC）
        QueryWrapper<CloudControllerResult> indexQueryWrapper = new QueryWrapper<>();
        indexQueryWrapper.lambda().eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName())
                .isNotNull(CloudControllerResult::getFileId)
                .ne(CloudControllerResult::getFileId, "")
                .and(wrapper -> wrapper
                    .gt(CloudControllerResult::getCreateTime, currentRecord.getCreateTime())
                    .or(subWrapper -> subWrapper
                        .eq(CloudControllerResult::getCreateTime, currentRecord.getCreateTime())
                        .gt(CloudControllerResult::getResultId, currentRecord.getResultId())
                    )
                );
        applySearchConditions(indexQueryWrapper, cloudControllerResultPageParam);
        long currentIndex = this.count(indexQueryWrapper);

        // 获取上一条记录（使用复合排序确保稳定性）
        QueryWrapper<CloudControllerResult> previousQueryWrapper = new QueryWrapper<>();
        previousQueryWrapper.lambda().eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName())
                .isNotNull(CloudControllerResult::getFileId)
                .ne(CloudControllerResult::getFileId, "")
                .and(wrapper -> wrapper
                    .gt(CloudControllerResult::getCreateTime, currentRecord.getCreateTime())
                    .or(subWrapper -> subWrapper
                        .eq(CloudControllerResult::getCreateTime, currentRecord.getCreateTime())
                        .gt(CloudControllerResult::getResultId, currentRecord.getResultId())
                    )
                )
                .orderByAsc(CloudControllerResult::getCreateTime)
                .orderByAsc(CloudControllerResult::getResultId)
                .last("LIMIT 1");
        applySearchConditions(previousQueryWrapper, cloudControllerResultPageParam);
        CloudControllerResult previousRecord = this.getOne(previousQueryWrapper);

        // 获取下一条记录（使用复合排序确保稳定性）
        QueryWrapper<CloudControllerResult> nextQueryWrapper = new QueryWrapper<>();
        nextQueryWrapper.lambda().eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName())
                .isNotNull(CloudControllerResult::getFileId)
                .ne(CloudControllerResult::getFileId, "")
                .and(wrapper -> wrapper
                    .lt(CloudControllerResult::getCreateTime, currentRecord.getCreateTime())
                    .or(subWrapper -> subWrapper
                        .eq(CloudControllerResult::getCreateTime, currentRecord.getCreateTime())
                        .lt(CloudControllerResult::getResultId, currentRecord.getResultId())
                    )
                )
                .orderByDesc(CloudControllerResult::getCreateTime)
                .orderByDesc(CloudControllerResult::getResultId)
                .last("LIMIT 1");
        applySearchConditions(nextQueryWrapper, cloudControllerResultPageParam);
        CloudControllerResult nextRecord = this.getOne(nextQueryWrapper);

        result.put("currentRecord", currentRecord);
        result.put("previousRecord", previousRecord);
        result.put("nextRecord", nextRecord);
        result.put("totalCount", totalCount);
        result.put("currentIndex", currentIndex);

        return result;
    }

    /**
     * 应用搜索条件到查询包装器
     */
    private void applySearchConditions(QueryWrapper<CloudControllerResult> queryWrapper, CloudControllerResultPageParam cloudControllerResultPageParam) {
        String searchKey = cloudControllerResultPageParam.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {
            String policyIdStr = null;
            String deviceId = null;
            if (StringUtils.equalsIgnoreCase(cloudControllerResultPageParam.getCategory(), "POLICY")) {
                policyIdStr = StringUtils.substringBefore(searchKey, "_");
                deviceId = StringUtils.substringAfter(searchKey, "_");
            } else if (StringUtils.equalsIgnoreCase(cloudControllerResultPageParam.getCategory(), "DEVICE")) {
                policyIdStr = StringUtils.substringAfterLast(searchKey, "_");
                deviceId = StringUtils.substringBeforeLast(searchKey, "_");
            }
            Integer policyId = null;
            if (StringUtils.equalsIgnoreCase("-1", policyIdStr)) {
                policyId = -1;
            } else if (StringUtils.isNumeric(policyIdStr)) {
                try {
                    policyId = Integer.valueOf(policyIdStr);
                } catch (NumberFormatException e) {
                    log.warn("解析策略ID异常，policyIdStr={}", policyIdStr, e);
                }
            }
            if (policyId != null) {
                cloudControllerResultPageParam.setPolicyId(policyId);
            }
            if (StringUtils.isNotBlank(deviceId)) {
                cloudControllerResultPageParam.setDeviceId(deviceId);
            }

        }

        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getLuaName())) {
            queryWrapper.lambda().eq(CloudControllerResult::getLuaName, cloudControllerResultPageParam.getLuaName());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getDeviceId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getDeviceId, cloudControllerResultPageParam.getDeviceId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getHostName())) {
            queryWrapper.lambda().like(CloudControllerResult::getHostName, cloudControllerResultPageParam.getHostName());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getFileId())) {
            queryWrapper.lambda().eq(CloudControllerResult::getFileId, cloudControllerResultPageParam.getFileId());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getDataJson())) {
            queryWrapper.lambda().like(CloudControllerResult::getDataJson, cloudControllerResultPageParam.getDataJson());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getAbResult())) {
            queryWrapper.lambda().like(CloudControllerResult::getAbResult, "\"" + cloudControllerResultPageParam.getAbResult() + "\"");
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getAbContent())) {
            queryWrapper.lambda().like(CloudControllerResult::getAbContent, cloudControllerResultPageParam.getAbContent());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getOcrContent())) {
            queryWrapper.lambda().like(CloudControllerResult::getOcrContent, cloudControllerResultPageParam.getOcrContent());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getStatus())) {
            queryWrapper.lambda().eq(CloudControllerResult::getStatus, cloudControllerResultPageParam.getStatus());
        }
        if (ObjectUtil.isNotEmpty(cloudControllerResultPageParam.getPolicyId())) {
            if (Integer.valueOf(-1).equals(cloudControllerResultPageParam.getPolicyId())) {
                queryWrapper.lambda().and(q -> q.isNull(CloudControllerResult::getPolicyIds).or().eq(CloudControllerResult::getPolicyIds, "").or().eq(CloudControllerResult::getPolicyIds, "[]"));
            } else {
                queryWrapper.lambda().inSql(CloudControllerResult::getResultId, String.format("select distinct result_id from tb_cloud_controller_policy_result_link where policy_id=%s", cloudControllerResultPageParam.getPolicyId()));
            }
        }
    }

    @Override
    public List<PolicyResultTreeDTO> policyResultTree() {
        List<PolicyResultTreeDTO> policyResultTreeDTOList = this.baseMapper.policyResultTree();
        if (CollectionUtils.isNotEmpty(policyResultTreeDTOList)) {
            for (PolicyResultTreeDTO policyResultTreeDTO : policyResultTreeDTOList) {
                List<PolicyResultTreeDTO> children = policyResultTreeDTO.getChildren();
                policyResultTreeDTO.setKey(String.valueOf(policyResultTreeDTO.getPolicyId()));
//                policyResultTreeDTO.setTitle(String.format("%s(%s)", policyResultTreeDTO.getPolicyName(), policyResultTreeDTO.getPolicyId()));
                policyResultTreeDTO.setTitle(policyResultTreeDTO.getPolicyName());
//                policyResultTreeDTO.setDisabled(CollectionUtils.isEmpty(children));
                if (CollectionUtils.isNotEmpty(children)) {
                    for (PolicyResultTreeDTO policyResultTreeChildrenDTO : children) {
                        policyResultTreeChildrenDTO.setKey(String.format("%s_%s", policyResultTreeDTO.getPolicyId(), policyResultTreeChildrenDTO.getDeviceId()));
//                        policyResultTreeChildrenDTO.setTitle(String.format("%s(%s)", policyResultTreeChildrenDTO.getHostName(), policyResultTreeChildrenDTO.getDeviceId()));
                        policyResultTreeChildrenDTO.setTitle(policyResultTreeChildrenDTO.getHostName());
                    }
                }
            }
        }
        return policyResultTreeDTOList;
    }

    @Override
    public List<PolicyResultTreeDTO> deviceResultTree() {
        List<PolicyResultTreeDTO> policyResultTreeDTOList = this.baseMapper.deviceResultTree();
        if (CollectionUtils.isNotEmpty(policyResultTreeDTOList)) {
            for (PolicyResultTreeDTO policyResultTreeDTO : policyResultTreeDTOList) {
                List<PolicyResultTreeDTO> children = policyResultTreeDTO.getChildren();
                policyResultTreeDTO.setKey(policyResultTreeDTO.getDeviceId());
//                policyResultTreeDTO.setTitle(String.format("%s(%s)", policyResultTreeDTO.getHostName(), policyResultTreeDTO.getDeviceId()));
                policyResultTreeDTO.setTitle(policyResultTreeDTO.getHostName());
//                policyResultTreeDTO.setDisabled(CollectionUtils.isEmpty(children));
                if (CollectionUtils.isNotEmpty(children)) {
                    for (PolicyResultTreeDTO policyResultTreeChildrenDTO : children) {
                        policyResultTreeChildrenDTO.setKey(String.format("%s_%s", policyResultTreeDTO.getDeviceId(), policyResultTreeChildrenDTO.getPolicyId()));
//                        policyResultTreeChildrenDTO.setTitle(String.format("%s(%s)", policyResultTreeChildrenDTO.getPolicyName(), policyResultTreeChildrenDTO.getPolicyId()));
                        policyResultTreeChildrenDTO.setTitle(policyResultTreeChildrenDTO.getPolicyName());
                    }
                }
            }
        }
        return policyResultTreeDTOList;
    }

    @Override
    public Page<PolicyResultTreeDTO> policyResultTreePage() {
        Page<String> policyPage = this.baseMapper.policyPage(CommonPageRequest.defaultPage());
        List<String> policyList = policyPage.getRecords();
        List<PolicyResultTreeDTO> policyResultTreeDTOList = this.baseMapper.policyResultTree(policyList);
        if (CollectionUtils.isNotEmpty(policyResultTreeDTOList)) {
            for (PolicyResultTreeDTO policyResultTreeDTO : policyResultTreeDTOList) {
                List<PolicyResultTreeDTO> children = policyResultTreeDTO.getChildren();
                policyResultTreeDTO.setKey(String.valueOf(policyResultTreeDTO.getPolicyId()));
//                policyResultTreeDTO.setTitle(String.format("%s(%s)", policyResultTreeDTO.getPolicyName(), policyResultTreeDTO.getPolicyId()));
                policyResultTreeDTO.setTitle(policyResultTreeDTO.getPolicyName());
//                policyResultTreeDTO.setDisabled(CollectionUtils.isEmpty(children));
                if (CollectionUtils.isNotEmpty(children)) {
                    for (PolicyResultTreeDTO policyResultTreeChildrenDTO : children) {
                        policyResultTreeChildrenDTO.setKey(String.format("%s_%s", policyResultTreeDTO.getPolicyId(), policyResultTreeChildrenDTO.getDeviceId()));
//                        policyResultTreeChildrenDTO.setTitle(String.format("%s(%s)", policyResultTreeChildrenDTO.getHostName(), policyResultTreeChildrenDTO.getDeviceId()));
                        policyResultTreeChildrenDTO.setTitle(policyResultTreeChildrenDTO.getHostName());
                    }
                }
            }
        }
        Page<PolicyResultTreeDTO> policyResultTreeDTOPage = CommonPageRequest.defaultPage();
        policyResultTreeDTOPage.setSize(policyPage.getSize());
        policyResultTreeDTOPage.setCurrent(policyPage.getCurrent());
        policyResultTreeDTOPage.setTotal(policyPage.getTotal());
        policyResultTreeDTOPage.setRecords(policyResultTreeDTOList);
        return policyResultTreeDTOPage;
    }

    @Override
    public Page<PolicyResultTreeDTO> deviceResultTreePage() {
        Page<String> devicePage = this.baseMapper.devicePage(CommonPageRequest.defaultPage());
        List<String> deviceList = devicePage.getRecords();
        List<PolicyResultTreeDTO> policyResultTreeDTOList = this.baseMapper.deviceResultTree(deviceList);
        if (CollectionUtils.isNotEmpty(policyResultTreeDTOList)) {
            for (PolicyResultTreeDTO policyResultTreeDTO : policyResultTreeDTOList) {
                List<PolicyResultTreeDTO> children = policyResultTreeDTO.getChildren();
                policyResultTreeDTO.setKey(policyResultTreeDTO.getDeviceId());
//                policyResultTreeDTO.setTitle(String.format("%s(%s)", policyResultTreeDTO.getHostName(), policyResultTreeDTO.getDeviceId()));
                policyResultTreeDTO.setTitle(policyResultTreeDTO.getHostName());
//                policyResultTreeDTO.setDisabled(CollectionUtils.isEmpty(children));
                if (CollectionUtils.isNotEmpty(children)) {
                    for (PolicyResultTreeDTO policyResultTreeChildrenDTO : children) {
                        policyResultTreeChildrenDTO.setKey(String.format("%s_%s", policyResultTreeDTO.getDeviceId(), policyResultTreeChildrenDTO.getPolicyId()));
//                        policyResultTreeChildrenDTO.setTitle(String.format("%s(%s)", policyResultTreeChildrenDTO.getPolicyName(), policyResultTreeChildrenDTO.getPolicyId()));
                        policyResultTreeChildrenDTO.setTitle(policyResultTreeChildrenDTO.getPolicyName());
                    }
                }
            }
        }
        Page<PolicyResultTreeDTO> policyResultTreeDTOPage = CommonPageRequest.defaultPage();
        policyResultTreeDTOPage.setSize(devicePage.getSize());
        policyResultTreeDTOPage.setCurrent(devicePage.getCurrent());
        policyResultTreeDTOPage.setTotal(devicePage.getTotal());
        policyResultTreeDTOPage.setRecords(policyResultTreeDTOList);
        return policyResultTreeDTOPage;
    }

    @Override
    public String rename(String filePath) {
        String destDir = filePath + "(重命名)";
        List<String> paths = FileUtil.listFileNames(filePath);
        if (CollectionUtils.isNotEmpty(paths)) {
            for (String fileName : paths) {
                String fileId = FileNameUtil.getPrefix(fileName);
                QueryWrapper<CloudControllerResult> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(CloudControllerResult::getFileId, fileId);
                CloudControllerResult cloudControllerResult = this.getOne(queryWrapper);
                String srcPath = filePath + File.separator + fileName;
                String destPath = destDir + File.separator;
                if (cloudControllerResult != null) {
                    destPath += this.getAbResult(cloudControllerResult) + File.separator;
//                    destPath += cloudControllerResult.getDeviceId() + "_" + DateFormatUtils.format(cloudControllerResult.getCreateTime(), "yyyyMMddHHmmss") + "_" + "[" + this.getAbResult(cloudControllerResult) + "]" + "_";
                    destPath += IdWorker.get32UUID() + cloudControllerResult.getDeviceId() + "_" + DateFormatUtils.format(cloudControllerResult.getCreateTime(), "yyyyMMddHHmmss") + "_" + "[" + this.getAbResult(cloudControllerResult) + "]" + "_";
                }
                destPath += fileName;
                FileUtil.copy(srcPath, destPath, Boolean.TRUE);
            }
        }

        return destDir;
    }

    /**
     * 获取异常分类结果
     *
     * @param cloudControllerResult 回传结果
     * @return 异常分类结果
     */
    private String getAbResult(CloudControllerResult cloudControllerResult) {
        Map<String, String> map = new HashMap<>();
        map.put("1", "恶意软件使用");
        map.put("2", "多开和群控");
        map.put("3", "租号号商");
        map.put("4", "易语言程序");
        map.put("5", "正常玩家");
        map.put("6", "可疑玩家");
        map.put("7", "外挂开发");
        map.put("-1", "未知");

        if (StringUtils.isNotBlank(cloudControllerResult.getAbResult())) {
            List<String> abResult = JSONObject.parseArray(cloudControllerResult.getAbResult(), String.class);
            Collections.sort(abResult);
            List<String> abResultZh = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(abResult)) {
                for (String s : abResult) {
                    abResultZh.add(map.getOrDefault(s, "未知"));
                }
            }
            return StringUtils.join(abResultZh, "_");
        }
        return "未知";
    }
}
