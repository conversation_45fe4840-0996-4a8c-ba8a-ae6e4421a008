package vip.xiaonuo.biz.modular.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import vip.xiaonuo.biz.core.constant.CommonConstant;
import vip.xiaonuo.biz.modular.message.service.MessageService;
import vip.xiaonuo.biz.modular.monitor.service.MonitorTagHourService;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 标签用户量定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/3/13 20:09
 */
@Component
@Slf4j
public class MonitorTagHourTimerTaskRunner implements CommonTimerTaskRunner {

    @Resource
    private MonitorTagHourService monitorTagHourService;

    @Resource
    private MessageService messageService;

    @Override
    public void action() {
        try {
            LocalDateTime startTime = LocalDateTime.now();
            log.info("开始执行导入标签用户量定时任务......");
            Boolean importResult = monitorTagHourService.importMonitorTagDay(null, CommonConstant.DEFAULT_DOMAIN_NAME_EN);
            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            if (importResult) {
                log.info(String.format("执行导入标签用户量定时任务成功,耗时=[%s]ms。", duration.toMillis()));
            } else {
                messageService.sendXingeAdministratorMessage("执行导入标签用户量定时任务失败", null);
                log.warn(String.format("执行导入标签用户量定时任务失败,耗时=[%s]ms。", duration.toMillis()));
            }
            // 发送标签告警(天)（离线标签）
            monitorTagHourService.sendDayTagAlarm(null);
            // 发送标签告警(天)（离线标签）(特定标签)
            monitorTagHourService.sendDayPartTagAlarm(null);

            // TODO 发送篡改客户端标签告警（实时标签）
            String tagNameEn = "is_login_mps_apk_certificate_sha1_ab_15d";
            String dataType = "INT";
            String tagValue = "2";
            Boolean sendDayRealTimeTagAlarmResult = monitorTagHourService.sendDayRealTimeTagAlarm(null, 1001L, tagNameEn, dataType, tagValue, Boolean.TRUE);
            if (sendDayRealTimeTagAlarmResult) {
                log.info("执行发送篡改客户端标签告警（实时标签）定时任务成功!");
            } else {
                messageService.sendXingeAdministratorMessage("执行发送篡改客户端标签告警（实时标签）定时任务失败", null);
                log.warn("执行发送篡改客户端标签告警（实时标签）定时任务失败!");
            }
        } catch (Exception e) {
            messageService.sendXingeAdministratorMessage("执行导入标签用户量定时任务失败", null);
            log.error("执行导入标签用户量定时任务失败!", e);
        }
    }
}
