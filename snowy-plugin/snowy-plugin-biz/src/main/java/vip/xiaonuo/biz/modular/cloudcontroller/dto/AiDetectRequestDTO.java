package vip.xiaonuo.biz.modular.cloudcontroller.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * AI识别请求数据传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 15:10
 */
@Data
public class AiDetectRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 图片索引
     */
    @JSONField(name = "image_index")
    private String imageIndex;

    /**
     * 图片Base64编码
     */
    @JSONField(name = "base64_image")
    private String base64Image;

    /**
     * 请求时间戳
     */
    private Long timestamp;

    /**
     * 请求ID（用于追踪）
     */
    private String requestId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 日期
     */
    private String day;
}
