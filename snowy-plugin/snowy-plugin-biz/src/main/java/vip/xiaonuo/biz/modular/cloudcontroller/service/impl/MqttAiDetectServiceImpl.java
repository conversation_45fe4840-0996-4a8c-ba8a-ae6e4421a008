package vip.xiaonuo.biz.modular.cloudcontroller.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import vip.xiaonuo.biz.core.config.MqttConfig;
import vip.xiaonuo.biz.core.constant.DataSourceConstant;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.AiDetectRequestDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.AiDetectResponseDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.entity.CloudControllerResult;
import vip.xiaonuo.biz.modular.cloudcontroller.service.CloudControllerResultService;
import vip.xiaonuo.biz.modular.cloudcontroller.service.MqttAiDetectService;
import vip.xiaonuo.biz.modular.remote.service.AIRemoteService;
import feign.Request;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * MQTT AI识别服务实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 15:20
 */
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_CLOUD)
@Service
@Slf4j
public class MqttAiDetectServiceImpl implements MqttAiDetectService {

    @Autowired(required = false)
    @Qualifier("mqttImageOutboundChannel")
    private MessageChannel mqttImageOutboundChannel;

    @Autowired(required = false)
    private MqttConfig mqttConfig;

    @Autowired
    private CloudControllerResultService cloudControllerResultService;

    @Autowired
    private AIRemoteService aiRemoteService;

    @Override
    public void sendAiDetectRequest(String resultId, String fileIdOfLuaScreenShot, String base64OfLuaScreenShot, String deviceId, String day) {
        if (mqttConfig == null || !mqttConfig.isMqttImageEnabled() || mqttImageOutboundChannel == null) {
            log.warn("MQTT图片识别未启用或未配置，跳过AI识别请求发送，resultId=[{}]", resultId);
            throw new RuntimeException("MQTT图片识别未启用或未配置");
        }

        try {
            AiDetectRequestDTO requestDTO = new AiDetectRequestDTO();
            requestDTO.setResultId(resultId);
            requestDTO.setImageIndex(fileIdOfLuaScreenShot);
            requestDTO.setBase64Image(base64OfLuaScreenShot);
            requestDTO.setTimestamp(System.currentTimeMillis());
            requestDTO.setRequestId(IdUtil.fastSimpleUUID());
            requestDTO.setDeviceId(deviceId);
            requestDTO.setDay(day);

            String messagePayload = JSONObject.toJSONString(requestDTO);

            Message<String> message = MessageBuilder
                    .withPayload(messagePayload)
                    .setHeader(MqttHeaders.TOPIC, mqttConfig.getAiImageRequestTopic())
                    .setHeader(MqttHeaders.QOS, 2)
                    .setHeader(MqttHeaders.RETAINED, false)
                    .build();

            mqttImageOutboundChannel.send(message);
            log.info("AI识别请求已发送到MQTT，resultId=[{}], requestId=[{}]", resultId, requestDTO.getRequestId());
        } catch (Exception e) {
            log.error("发送AI识别请求到MQTT失败，resultId=[{}]", resultId, e);
        }
    }


    @Override
    public void handleAiDetectResponse(AiDetectResponseDTO responseDTO) {
        if (responseDTO == null || StringUtils.isBlank(responseDTO.getResultId())) {
            log.warn("AI识别响应数据无效：{}", JSONObject.toJSONString(responseDTO));
            return;
        }

        try {
            CloudControllerResult cloudControllerResult = cloudControllerResultService.queryEntity(responseDTO.getResultId());
            if (cloudControllerResult == null) {
                log.warn("未找到对应的结果记录，resultId=[{}]", responseDTO.getResultId());
                return;
            }

            if (responseDTO.getSuccess() == null || responseDTO.getSuccess()) {
                // 更新识别结果 - 合并模式
                if (StringUtils.isNotBlank(responseDTO.getResult())) {
                    String mergedAbResult = mergeAiDetectResults(cloudControllerResult.getAbResult(), responseDTO.getResult());
                    cloudControllerResult.setAbResult(mergedAbResult);
                }
                if (StringUtils.isNotBlank(responseDTO.getContent())) {
                    cloudControllerResult.setAbContent(responseDTO.getContent());
                }
                if (StringUtils.isNotBlank(responseDTO.getOcr())) {
                    cloudControllerResult.setOcrContent(responseDTO.getOcr());
                }

                cloudControllerResultService.updateById(cloudControllerResult);
                log.info("AI识别结果已更新，resultId=[{}], requestId=[{}]",
                        responseDTO.getResultId(), responseDTO.getRequestId());
            } else {
                log.error("AI识别失败，resultId=[{}], requestId=[{}], error=[{}]",
                        responseDTO.getResultId(), responseDTO.getRequestId(), responseDTO.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("处理AI识别响应失败，resultId=[{}]", responseDTO.getResultId(), e);
        }
    }

    @Override
    public AiDetectResponseDTO detectDirect(AiDetectRequestDTO requestDTO) {
        AiDetectResponseDTO responseDTO = new AiDetectResponseDTO();
        responseDTO.setResultId(requestDTO.getResultId());
        responseDTO.setRequestId(requestDTO.getRequestId());
        responseDTO.setTimestamp(System.currentTimeMillis());

        try {
            LocalDateTime startTime = LocalDateTime.now();
            JSONObject params = new JSONObject();
            params.put("image_index", requestDTO.getImageIndex());
            params.put("base64_image", requestDTO.getBase64Image());
            params.put("deviceId", requestDTO.getDeviceId());
            params.put("day", requestDTO.getDay());

            // 自定义超时时间
            Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 10);
            JSONObject remoteResult = aiRemoteService.detect(options, params);

            if (remoteResult != null && (StringUtils.isNotBlank(remoteResult.getString("result")) ||
                    StringUtils.isNotBlank(remoteResult.getString("ocr")))) {
                responseDTO.setSuccess(true);
                if (StringUtils.isNotBlank(remoteResult.getString("result"))) {
                    responseDTO.setResult(JSONObject.toJSONString(remoteResult.getJSONArray("result")));
                }
                if (StringUtils.isNotBlank(remoteResult.getString("content"))) {
                    responseDTO.setContent(remoteResult.getString("content"));
                }
                if (StringUtils.isNotBlank(remoteResult.getString("ocr"))) {
                    responseDTO.setOcr(remoteResult.getString("ocr"));
                }
            } else {
                responseDTO.setSuccess(false);
                responseDTO.setErrorMessage("AI识别接口返回结果为空");
            }

            LocalDateTime endTime = LocalDateTime.now();
            Duration duration = Duration.between(startTime, endTime);
            log.info("直接调用AI识别接口完成，耗时=[{}]ms，resultId=[{}]",
                    duration.toMillis(), requestDTO.getResultId());

        } catch (Exception e) {
            log.error("直接调用AI识别接口失败，resultId=[{}]", requestDTO.getResultId(), e);
            responseDTO.setSuccess(false);
            responseDTO.setErrorMessage(e.getMessage());
        }

        return responseDTO;
    }

    /**
     * 合并AI识别结果
     *
     * @param existingResult 现有的识别结果JSON字符串
     * @param newResult      新的识别结果JSON字符串
     * @return 合并后的识别结果JSON字符串
     */
    private String mergeAiDetectResults(String existingResult, String newResult) {
        try {
            Set<String> mergedResults = new LinkedHashSet<>();

            // 解析现有结果
            if (StringUtils.isNotBlank(existingResult)) {
                JSONArray existingArray = JSONObject.parseArray(existingResult);
                if (existingArray != null) {
                    for (Object item : existingArray) {
                        if (item != null) {
                            mergedResults.add(item.toString());
                        }
                    }
                }
            }

            // 解析新结果
            if (StringUtils.isNotBlank(newResult)) {
                JSONArray newArray = JSONObject.parseArray(newResult);
                if (newArray != null) {
                    for (Object item : newArray) {
                        if (item != null) {
                            mergedResults.add(item.toString());
                        }
                    }
                }
            }

            // 转换为排序后的列表
            List<String> sortedResults = new ArrayList<>(mergedResults);
            Collections.sort(sortedResults);

            return JSONObject.toJSONString(sortedResults);
        } catch (Exception e) {
            log.error("合并AI识别结果失败，existingResult=[{}], newResult=[{}]", existingResult, newResult, e);
            // 如果合并失败，返回新结果
            return StringUtils.isNotBlank(newResult) ? JSONObject.toJSONString(JSONObject.parseArray(newResult)) : existingResult;
        }
    }
}
