package vip.xiaonuo.biz.modular.cloudcontroller.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import feign.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vip.xiaonuo.biz.modular.remote.service.AIRemoteService;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.annotation.Resource;
import java.io.File;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * AI远程调用控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/6/9 16:10
 */
@Api(tags = "AI远程调用控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
@RequestMapping("biz/cloudcontroller/aiRemote")
@Slf4j
public class AIRemoteController {

    @Resource
    private AIRemoteService aiRemoteService;

    /**
     * AI识图性能测试(4090)
     *
     * @param imageDir 图片文件目录路径
     * @return 性能测试报告文件路径
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation("AI识图性能测试(4090)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imageDir", value = "图片文件目录路径", paramType = "query", required = true, dataType = "String"),
    })
    @GetMapping("test4090Performance")
    public CommonResult<String> test4090Performance(@RequestParam(value = "imageDir", required = true) String imageDir) {
        return CommonResult.data(performAIDetectionTest(imageDir, "4090"));
    }

    /**
     * AI识图性能测试(3090)
     *
     * @param imageDir 图片文件目录路径
     * @return 性能测试报告文件路径
     */
    @ApiOperationSupport(order = 11)
    @ApiOperation("AI识图性能测试(3090)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imageDir", value = "图片文件目录路径", paramType = "query", required = true, dataType = "String"),
    })
    @GetMapping("test3090Performance")
    public CommonResult<String> test3090Performance(@RequestParam(value = "imageDir", required = true) String imageDir) {
        return CommonResult.data(performAIDetectionTest(imageDir, "3090"));
    }

    /**
     * 执行AI识图性能测试
     *
     * @param imageDir 图片目录路径
     * @param gpuType  GPU类型（4090或3090）
     * @return 测试报告文件路径
     */
    private String performAIDetectionTest(String imageDir, String gpuType) {
        log.info("开始执行AI识图性能测试，GPU类型：{}，图片目录：{}", gpuType, imageDir);

        // 验证目录是否存在
        File dir = new File(imageDir);
        if (!dir.exists() || !dir.isDirectory()) {
            throw new RuntimeException("指定的图片目录不存在或不是有效目录：" + imageDir);
        }

        // 获取目录中的所有图片文件
        List<File> imageFiles = getImageFiles(dir);
        if (imageFiles.isEmpty()) {
            throw new RuntimeException("指定目录中没有找到图片文件：" + imageDir);
        }

        log.info("找到 {} 张图片文件，开始性能测试", imageFiles.size());

        // 性能测试数据收集
        List<String> testResults = new ArrayList<>();
        testResults.add("AI识图性能测试报告");
        testResults.add("GPU类型：" + gpuType);
        testResults.add("测试时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        testResults.add("图片目录：" + imageDir);
        testResults.add("图片总数：" + imageFiles.size());
        testResults.add("========================================");
        testResults.add("");

        long totalDuration = 0;
        int successCount = 0;
        int failureCount = 0;

        // 遍历每张图片文件进行测试
        for (int i = 0; i < imageFiles.size(); i++) {
            File imageFile = imageFiles.get(i);
            log.info("正在测试第 {}/{} 张图片：{}", i + 1, imageFiles.size(), imageFile.getName());

            try {
                // 记录开始时间
                LocalDateTime startTime = LocalDateTime.now();

                // 将图片转换为base64
                byte[] imageBytes = FileUtil.readBytes(imageFile);
                String base64Image = Base64.encodeBase64String(imageBytes);

                // 构建请求参数
                JSONObject params = new JSONObject();
                params.put("base64_image", base64Image);

                // 设置超时时间
                Request.Options options = new Request.Options(10 * 1000, 60 * 1000 * 10);

                // 调用对应的AI接口
                JSONObject result;
                if ("4090".equals(gpuType)) {
                    result = aiRemoteService.detect4090(options, params);
                } else {
                    result = aiRemoteService.detect3090(options, params);
                }

                // 记录结束时间
                LocalDateTime endTime = LocalDateTime.now();
                Duration duration = Duration.between(startTime, endTime);
                long durationMs = duration.toMillis();
                totalDuration += durationMs;

                // 判断调用是否成功
                if (result != null) {
                    successCount++;
                    String testResult = String.format("第%d张图片：%s - 成功 - 耗时：%dms  - 远程返回结果：%s",
                            i + 1, imageFile.getName(), durationMs, JSONObject.toJSONString(result));
                    log.info(testResult);
                    testResults.add(testResult);
                } else {
                    failureCount++;
                    String testResult = String.format("第%d张图片：%s - 失败（返回null） - 耗时：%dms",
                            i + 1, imageFile.getName(), durationMs);
                    log.info(testResult);
                    testResults.add(testResult);
                }

            } catch (Exception e) {
                failureCount++;
                log.error("测试图片 {} 时发生异常", imageFile.getName(), e);
                testResults.add(String.format("第%d张图片：%s - 异常：%s",
                        i + 1, imageFile.getName(), e.getMessage()));
            }
        }

        // 计算统计信息
        double averageDuration = imageFiles.size() > 0 ? (double) totalDuration / imageFiles.size() : 0;

        testResults.add("");
        testResults.add("========================================");
        testResults.add("测试统计结果：");
        testResults.add("总图片数：" + imageFiles.size());
        testResults.add("成功数：" + successCount);
        testResults.add("失败数：" + failureCount);
        testResults.add("成功率：" + String.format("%.2f%%", (double) successCount / imageFiles.size() * 100));
        testResults.add("总耗时：" + totalDuration + "ms");
        testResults.add("平均耗时：" + String.format("%.2f", averageDuration) + "ms");
        testResults.add("测试完成时间：" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 生成测试报告文件
        String reportFileName = String.format("AI_Performance_Test_%s_%s.txt",
                gpuType, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        String reportFilePath = System.getProperty("java.io.tmpdir") + File.separator + reportFileName;
//        String reportFilePath = reportFileName;

        try {
            FileUtil.writeLines(testResults, reportFilePath, "UTF-8");
            log.info("性能测试完成，测试结果：\r\n{}\r\n报告文件已生成：{}", testResults, reportFilePath);
            return reportFilePath;
        } catch (Exception e) {
            log.error("生成测试报告文件失败", e);
            throw new RuntimeException("生成测试报告文件失败：" + e.getMessage());
        }
    }

    /**
     * 获取目录中的所有图片文件
     *
     * @param dir 目录
     * @return 图片文件列表
     */
    private List<File> getImageFiles(File dir) {
        List<File> imageFiles = new ArrayList<>();
        File[] files = dir.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isFile() && isImageFile(file.getName())) {
                    imageFiles.add(file);
                }
            }
        }

        return imageFiles;
    }

    /**
     * 判断文件是否为图片文件
     *
     * @param fileName 文件名
     * @return 是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }

        String extension = StrUtil.subAfter(fileName, ".", true).toLowerCase();
        return "jpg".equals(extension) || "jpeg".equals(extension) ||
                "png".equals(extension) || "bmp".equals(extension) ||
                "gif".equals(extension) || "tiff".equals(extension) ||
                "webp".equals(extension);
    }
}
