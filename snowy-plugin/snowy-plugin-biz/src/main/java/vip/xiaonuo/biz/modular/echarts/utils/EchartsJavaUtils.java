package vip.xiaonuo.biz.modular.echarts.utils;

import com.alibaba.fastjson.JSONObject;
import org.icepear.echarts.Line;
import org.icepear.echarts.Pie;
import org.icepear.echarts.charts.line.LineSeries;
import org.icepear.echarts.charts.pie.PieEmphasis;
import org.icepear.echarts.charts.pie.PieItemStyle;
import org.icepear.echarts.charts.pie.PieLabel;
import org.icepear.echarts.charts.pie.PieSeries;
import org.icepear.echarts.components.coord.AxisNameTextStyle;
import org.icepear.echarts.components.coord.CategoryAxisTick;
import org.icepear.echarts.components.coord.ValueAxisLabel;
import org.icepear.echarts.components.coord.cartesian.CategoryAxis;
import org.icepear.echarts.components.coord.cartesian.ValueAxis;
import org.icepear.echarts.components.dataset.Dataset;
import org.icepear.echarts.components.grid.Grid;
import org.icepear.echarts.components.legend.Legend;
import org.icepear.echarts.components.marker.*;
import org.icepear.echarts.components.series.SeriesLabel;
import org.icepear.echarts.components.series.SeriesLineLabel;
import org.icepear.echarts.components.text.LabelLayout;
import org.icepear.echarts.components.title.Title;
import org.icepear.echarts.components.tooltip.Tooltip;
import org.icepear.echarts.origin.component.marker.MarkLineDataItemOption;
import org.icepear.echarts.origin.component.marker.MarkPointDataItemOption;
import org.icepear.echarts.render.Engine;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/9/27 14:04
 */
public class EchartsJavaUtils {
    /**
     * 折线图
     *
     * @return Line
     */
    public static Line defaultLineStyle(String titleText, String yAxesName, String yAxesFormatter, String xAxesName, Object[] xAxesData) {
        // All methods in EChart Java supports method chaining
        Line line = new Line()
                .setTitle(new Title().setText(titleText).setLeft("center"))
                .setLegend(new Legend().setShow(Boolean.TRUE).setOrient("horizontal").setBottom("1%"))
                .setTooltip(new Tooltip().setTrigger("axis"))
                .addYAxis(new ValueAxis().setName(yAxesName).setNameLocation("center").setNameGap(50).setNameTextStyle(new AxisNameTextStyle().setLineHeight(20)).setAxisLabel(new ValueAxisLabel().setFormatter(yAxesFormatter).setHideOverlap(Boolean.TRUE)))
                .addXAxis(new CategoryAxis().setName(xAxesName).setData(xAxesData).setNameLocation("center").setNameTextStyle(new AxisNameTextStyle().setLineHeight(30)).setAxisTick(new CategoryAxisTick().setAlignWithLabel(Boolean.TRUE)));

        line.getOption().setAnimation(Boolean.FALSE).setBackgroundColor("#fff").setColor(new String[]{"#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc", "#626c91", "#c9ab00", "#c14089"});
        // 显示完整刻度
        // .setGrid(new Grid().setContainLabel(Boolean.TRUE))
        return line;
    }

    /**
     * 折线图LineSeries
     *
     * @return LineSeries
     */
    public static LineSeries defaultLineSeriesStyle() {
        LineSeries lineSeries = new LineSeries();
        lineSeries.setLabel(new SeriesLabel().setShow(Boolean.TRUE));
        lineSeries.setLabelLayout(new LabelLayout().setHideOverlap(Boolean.TRUE));
        lineSeries.setEndLabel(new SeriesLabel().setShow(Boolean.TRUE).setFormatter("{a}"));
        return lineSeries;
    }

    /**
     * 折线图LineSeries(标记最大值、最小值和平均值)
     *
     * @return LineSeries
     */
    public static LineSeries defaultLineSeriesStyleWithMark() {
        LineSeries lineSeries = new LineSeries();
//        lineSeries.setLabel(new SeriesLabel().setShow(Boolean.TRUE));
        lineSeries.setLabelLayout(new LabelLayout().setHideOverlap(Boolean.TRUE));
//        lineSeries.setEndLabel(new SeriesLabel().setShow(Boolean.TRUE).setFormatter("{a}"));

        MarkPointDataItemOption[] markPointDataItemOptions = new MarkPointDataItemOption[]{new MarkPointDataItem().setType("max").setName("最大值"), new MarkPointDataItem().setType("min").setName("最小值")};
        lineSeries.setMarkPoint(new MarkPoint().setData(markPointDataItemOptions).setLabel(new SeriesLabel().setFormatter("{@score}%").setPosition("top")).setSymbol("circle").setSymbolSize(10));

        MarkLineDataItemOption[] markLineDataItemOptions = new MarkLineDataItemOption[]{new MarkLine1DDataItem().setType("average").setName("均值")};
        lineSeries.setMarkLine(new MarkLine().setData(markLineDataItemOptions).setSymbol("none").setLabel(new SeriesLineLabel().setFormatter("{c}%\n平均值")));

        return lineSeries;
    }

    /**
     * 饼图
     *
     * @param titleText 标题文本
     * @return Pie
     */
    public static Pie defaultPieStyle(String titleText) {
        // All methods in EChart Java supports method chaining
        Pie pie = new Pie()
                .setTitle(new Title().setText(titleText).setLeft("center"))
//                .setTitle(new Title().setText(titleText).setLeft("center").setSubtext("昨日各游戏告警比赛场地分布"))
                .setLegend(new Legend().setShow(Boolean.TRUE).setOrient("vertical").setLeft("left"))
                .setTooltip(new Tooltip().setTrigger("item").setFormatter("{a} <br/>{b} <br/>{d}%({c})"));

        pie.getOption().setAnimation(Boolean.FALSE).setBackgroundColor("#fff").setColor(new String[]{"#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc", "#626c91", "#c9ab00", "#c14089"});
        return pie;
    }

    /**
     * 饼图PieSeries
     *
     * @return PieSeries
     */
    public static PieSeries defaultPieSeriesStyle() {
        PieSeries pieSeries = new PieSeries();
        pieSeries.setRadius("75%");
        pieSeries.setCenter(new String[]{"50%", "50%"});

        // 设置强调样式
        pieSeries.setEmphasis(new PieEmphasis()
                .setItemStyle(new PieItemStyle()
                        .setShadowBlur(10)
                        .setShadowOffsetX(0)
                        .setShadowColor("rgba(0, 0, 0, 0.5)")));

        // 设置默认标签样式
        pieSeries.setLabel(new PieLabel()
                .setShow(Boolean.TRUE)
                .setPosition("outside")
                .setFormatter("{b}\r\n{d}%({c})"));

        return pieSeries;
    }

    public static void main(String[] args) {
//        Line line = defaultLineStyle("平均值趋势", "平均值(%)", "月份", new String[]{"2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08"});
//
//        LineSeries lineSeries = defaultLineSeriesStyle();
//        lineSeries.setData(new Number[]{22.91, 51.84, 24.77, 13.01, 12.83, 21.09});
//        lineSeries.setName("错误影响用户率");
//        line.addSeries(lineSeries);

        Line line = defaultLineStyle("平均值趋势", "平均值(%)", null, "月份", null);

        Dataset dataset = new Dataset();
        String[] dimensions = new String[]{"product", "2015", "2016", "2017"};
        dataset.setDimensions(dimensions);
        dataset.setSource(new Object[]{
                JSONObject.parseObject("{\"product\": \"Matcha Latte\", \"2015\": 43.3, \"2016\": 85.8, \"2017\": 93.7}")
                , JSONObject.parseObject("{\"product\": \"Milk Tea\", \"2015\": 83.1, \"2016\": 73.4, \"2017\": 55.1}")
                , JSONObject.parseObject("{\"product\": \"Cheese Cocoa\", \"2015\": 86.4, \"2016\": 65.2, \"2017\": 82.5}")
                , JSONObject.parseObject("{\"product\": \"Walnut Brownie\", \"2015\": 72.4, \"2016\": 59.3, \"2017\": 39.1}")
        });
        line.addDataset(dataset);

        for (int i = 1; i < dimensions.length; i++) {
            LineSeries lineSeries = defaultLineSeriesStyleWithMark();
            lineSeries.setName(dimensions[i] + "年");

            line.addSeries(lineSeries);
        }

        Engine engine = new Engine();
        // The renderJsonOption method will return a string, which represents an Option object in JSON format.
        String optionString = engine.renderJsonOption(line);

        System.out.println(optionString);

    }
}
