package vip.xiaonuo.biz.modular.cloudcontroller.controller;

import cn.hutool.core.util.IdUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.AiDetectRequestDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.dto.AiDetectResponseDTO;
import vip.xiaonuo.biz.modular.cloudcontroller.service.MqttAiDetectService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

/**
 * MQTT AI识别控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/21 16:00
 */
@Api(tags = "MQTT AI图片识别")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
@Slf4j
@RequestMapping("biz/cloudcontroller/mqttAi")
public class MqttAiDetectController {

    @Autowired
    private MqttAiDetectService mqttAiDetectService;

    /**
     * 直接调用AI识别接口
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("直接调用AI识别接口")
//    @CommonLog("直接调用AI识别接口")
    @PostMapping("detect")
    public CommonResult<AiDetectResponseDTO> detectDirect(@RequestBody @Validated AiDetectRequestDTO requestDTO) {
        // 如果没有提供requestId，则自动生成一个
        if (StringUtils.isBlank(requestDTO.getRequestId())) {
            requestDTO.setRequestId(IdUtil.fastSimpleUUID());
        }

        // 设置请求时间戳
        if (requestDTO.getTimestamp() == null) {
            requestDTO.setTimestamp(System.currentTimeMillis());
        }

        AiDetectResponseDTO responseDTO = mqttAiDetectService.detectDirect(requestDTO);
        return CommonResult.data(responseDTO);
    }

    /**
     * 发送AI识别请求到MQTT
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("发送AI识别请求到MQTT")
//    @CommonLog("发送AI识别请求到MQTT")
    @PostMapping("send")
    public CommonResult<String> sendAiDetectRequest(@RequestBody @Validated AiDetectRequestDTO requestDTO) {
        try {
            mqttAiDetectService.sendAiDetectRequest(requestDTO.getResultId(), requestDTO.getImageIndex(), requestDTO.getBase64Image(), requestDTO.getDeviceId(), requestDTO.getDay());
            return CommonResult.data("AI识别请求已发送到MQTT");
        } catch (Exception e) {
            log.error("发送AI识别请求到MQTT失败", e);
            return CommonResult.error("发送AI识别请求到MQTT失败：" + e.getMessage());
        }
    }

    /**
     * AI识别系统回调接口 - 接收识别结果
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("AI识别系统回调接口")
//    @CommonLog("AI识别系统回调接口")
    @PostMapping("callback")
    public CommonResult<String> receiveAiDetectResult(@RequestBody @Validated AiDetectResponseDTO responseDTO) {
        try {
            log.info("收到AI识别系统回调结果，resultId=[{}], requestId=[{}]",
                    responseDTO.getResultId(), responseDTO.getRequestId());

            // 处理AI识别响应
            mqttAiDetectService.handleAiDetectResponse(responseDTO);

            return CommonResult.data("AI识别结果处理成功");
        } catch (Exception e) {
            log.error("处理AI识别回调结果失败，resultId=[{}]", responseDTO.getResultId(), e);
            return CommonResult.error("处理AI识别结果失败：" + e.getMessage());
        }
    }
}
