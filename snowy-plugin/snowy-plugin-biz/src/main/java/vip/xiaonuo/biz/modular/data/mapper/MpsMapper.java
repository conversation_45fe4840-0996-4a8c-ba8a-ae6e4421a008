package vip.xiaonuo.biz.modular.data.mapper;

import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.biz.modular.data.dto.DataCntDTO;

import java.util.List;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/6/13 14:14
 */
public interface MpsMapper {
    /**
     * 获取数据量(天)
     *
     * @param dayStart     开始日期
     * @param dayEnd       结束日期
     * @param groupByAppId 是否按应用ID分组统计
     * @return 数据量
     */
    List<DataCntDTO> listDayDataCnt(@Param("dayStart") String dayStart, @Param("dayEnd") String dayEnd, @Param("groupByAppId") Boolean groupByAppId);

    /**
     * 获取数据量(小时)
     *
     * @param dayStart 开始日期
     * @param dayEnd   结束日期
     * @param hour     指定小时
     * @return 数据量
     */
    List<DataCntDTO> listHourDataCnt(@Param("dayStart") String dayStart, @Param("dayEnd") String dayEnd, @Param("hour") Integer hour);

    /**
     * 获取命中用户数(天)
     *
     * @param dayStart  开始日期
     * @param dayEnd    结束日期
     * @param appIds    appId
     * @param fieldName 字段,root_record_time、emulator1_record_time
     * @return 用户数
     */
    List<DataCntDTO> listDayHitUserIdCnt(@Param("dayStart") String dayStart, @Param("dayEnd") String dayEnd, @Param("appIds") List<Long> appIds, @Param("fieldName") String fieldName);
}
