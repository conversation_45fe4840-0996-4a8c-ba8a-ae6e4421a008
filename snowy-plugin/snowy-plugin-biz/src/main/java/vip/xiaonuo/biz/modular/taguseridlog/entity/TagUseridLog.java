/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.taguseridlog.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 标签用户触发日志记录表实体
 *
 * <AUTHOR>
 * @date  2024/01/07 23:20
 **/
@Getter
@Setter
@TableName("tb_mps_monitor_tag_userid_log")
public class TagUseridLog {

    /** 记录ID */
    @TableId
    @ApiModelProperty(value = "记录ID", position = 1)
    private String id;

    /** 日期 */
    @ApiModelProperty(value = "日期", position = 2)
    private String day;

    /** 用户ID */
    @ApiModelProperty(value = "用户ID", position = 3)
    private Long userId;

    /** 标签英文名 */
    @ApiModelProperty(value = "标签英文名", position = 4)
    private String tagNameEn;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 5)
    private String remark;

    /** 创建人 */
    @ApiModelProperty(value = "创建人", position = 6)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", position = 7)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 最后更新人 */
    @ApiModelProperty(value = "最后更新人", position = 8)
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    /** 最后更新时间 */
    @ApiModelProperty(value = "最后更新时间", position = 9)
    @TableField(fill = FieldFill.INSERT)
    private Date updateTime;

    /** 状态，-1数据无效，0不发送告警，1发送告警 */
    @ApiModelProperty(value = "状态，-1数据无效，0不发送告警，1发送告警", position = 10)
    private String status;
}
