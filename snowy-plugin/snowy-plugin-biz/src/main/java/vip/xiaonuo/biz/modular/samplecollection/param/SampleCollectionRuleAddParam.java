/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.samplecollection.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 样本采集规则表添加参数
 *
 * <AUTHOR>
 * @date 2023/03/23 11:22
 **/
@Getter
@Setter
public class SampleCollectionRuleAddParam {

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型", required = true, position = 2)
    @NotBlank(message = "dataType不能为空")
    private String dataType;

    /**
     * 维度
     */
    @ApiModelProperty(value = "维度", position = 3)
    private String dim;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", position = 4)
    private String ruleNameZh;

    /**
     * 规则英文名
     */
    @ApiModelProperty(value = "规则英文名", required = true, position = 5)
    @NotBlank(message = "ruleNameEn不能为空")
    private String ruleNameEn;

    /**
     * 规则条件JSON
     */
    @ApiModelProperty(value = "规则条件JSON", position = 6)
    private String ruleConditionJson;

    /**
     * 规则条件drl
     */
    @ApiModelProperty(value = "规则条件drl", position = 7)
    private String ruleConditionDrl;

    /**
     * 规则状态
     */
    @ApiModelProperty(value = "规则状态", required = true, position = 8)
    @NotBlank(message = "ruleStatus不能为空")
    private String ruleStatus;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", position = 9)
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true, position = 14)
    @NotBlank(message = "status不能为空")
    private String status;

}
