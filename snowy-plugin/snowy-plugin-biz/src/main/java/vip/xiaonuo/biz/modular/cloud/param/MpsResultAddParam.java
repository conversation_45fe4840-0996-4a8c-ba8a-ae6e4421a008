/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.cloud.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 安全策略下发联动-结果表添加参数
 *
 * <AUTHOR>
 * @date 2024/07/05 17:00
 **/
@Getter
@Setter
public class MpsResultAddParam {

    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", position = 2)
    private String deviceId;

    /**
     * JJ设备ID
     */
    @ApiModelProperty(value = "JJ设备ID", position = 3)
    private String jjDeviceId;

    /**
     * 任务名,JSONString
     */
    @ApiModelProperty(value = "任务名,JSONString", position = 4)
    private String taskName;

    /**
     * 策略ID,JSONString
     */
    @ApiModelProperty(value = "策略ID,JSONString", position = 5)
    private String policyIds;

    /**
     * 记录ID,JSONString
     */
    @ApiModelProperty(value = "记录ID,JSONString", position = 6)
    private String recordIds;

    /**
     * 文件ID
     */
    @ApiModelProperty(value = "文件ID", position = 7)
    private String fileId;

    /**
     * 数据
     */
    @ApiModelProperty(value = "数据", position = 8)
    private String dataJson;

    /**
     * 异常分类结果
     */
    @ApiModelProperty(value = "异常分类结果", position = 9)
    private String abResult;

    /**
     * 异常分类原因
     */
    @ApiModelProperty(value = "异常分类原因", position = 10)
    private String abContent;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", position = 11)
    private String remark;

    /**
     * 状态，-1删除，0无效，1有效
     */
    @ApiModelProperty(value = "状态，-1删除，0无效，1有效", position = 16)
    private String status;

}
