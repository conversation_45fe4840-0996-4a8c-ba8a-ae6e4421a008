/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.alarm.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 告警基本信息实体
 *
 * <AUTHOR>
 * @date 2023/03/31 11:07
 **/
@Getter
@Setter
@TableName("tb_mps_monitor_alarm")
public class Alarm {

    /**
     * 告警基本信息ID
     */
    @TableId
    @ApiModelProperty(value = "告警基本信息ID", position = 1)
    private String id;

    /**
     * 告警名称
     */
    @ApiModelProperty(value = "告警名称", position = 2)
    private String alarmName;

    /**
     * 统计周期
     */
    @ApiModelProperty(value = "统计周期", position = 3)
    private String alarmType;

    /**
     * 接收方式
     */
    @ApiModelProperty(value = "接收方式", position = 4)
    private String notifyType;

    /**
     * 比赛产品ID
     */
    @ApiModelProperty(value = "比赛产品ID", position = 5)
    private Long mpId;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", position = 6)
    private String receiveUser;

    /**
     * 抄送人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "抄送人", position = 7)
    private String ccUser;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", position = 8)
    private String description;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", position = 9)
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 10)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人", position = 11)
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间", position = 12)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", position = 13)
    private String status;
}
