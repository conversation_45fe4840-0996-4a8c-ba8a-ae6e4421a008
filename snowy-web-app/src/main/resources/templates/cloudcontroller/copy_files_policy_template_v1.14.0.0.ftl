
// policy_record_id = ${policyRecordId?c}
    match actor, device_id with
        | "service", "${deviceId}" -> {
            base_config = ${baseConfig}_config;
<#if luaTask.hasParams=='YES'>
            params = [
<#list paramsStr?keys as key>
                {
                    param = ${(key)!""};
                    policy_id_list = [${(paramsStr[key])!""}];
                }
				<#if key_has_next>;</#if>
</#list>
            ];
            policy_id_list = [];
<#else>
            params = [];
</#if>

// 扩展参数 start
<#if (extendedParams?? && extendedParams?size>0)>
            extended_params = [
<#list extendedParams?values as policyTemplatEextendedParamsDTO>
                {
                    param = [
					<#list policyTemplatEextendedParamsDTO.extendedParamsOfLuaTaskStr?keys as key>
						{
							name = ${(key)!""};
							value = ${(policyTemplatEextendedParamsDTO.extendedParamsOfLuaTaskStr[key])!""};
						}
					<#if key_has_next>;</#if>
					</#list>
					];
                    policy_id_list = [${(policyTemplatEextendedParamsDTO.policyIdListOfExtendedParamsStr)!""}];
                }
				<#if policyTemplatEextendedParamsDTO_has_next>;</#if>
</#list>
            ];
            policy_id_list = [];
</#if>
// 扩展参数 end

            policy_record_id = ${policyRecordId?c};
        };
