name: "dps"

type lua_function_id =
    | Enum_process          (1)
    | Enum_window_name      (2)
    | Create_screen_shot    (3)
    | Traverse_directory    (4)
    | Upload_files          (5)
    | Enum_process_module   (6)
    | Install_service       (7)
    | Uninstall_service     (8)

type trigger_type =
    | Exit_trigger      (0)
    | Log_trigger       (1)


params:
    app_id: int,
    os_version: int,
    channel_id: int,
    lua_function_id: lua_function_id,
    int_reserved_5: int,
    int_reserved_6: int,
    int_reserved_7: int,
    int_reserved_8: int,
    sdk_version: str, // x.x.x.x
    host_name: str,
    device_id: str,
    actor: str,
    str_reserved_5: str,
    str_reserved_6: str,
    str_reserved_7: str,
    str_reserved_8: str


type taskid =
    | None (0)
    | ProcessChecker (1)
    | ModuleChecker(2)

// task configuration definition ====================

type plain_json = {
    name : str;
    value : str;
}

type native_task_sub_config = {
    luacode: list of str;
    trigger: trigger_type;
    start: bool;
}

type native_task_config = {
    name : str;
    configs: native_task_sub_config;
}

type aes_params = {
    key: str;
    iv: str;
}

type lua_base_task_config = {
    name : str;
    id: int;
    is_force_execution: bool;
    is_save: bool;
    luacode: list of str;
    aes: aes_params;
    has_extra_info: bool;
    result_name : str;
    info : list of plain_json;
}

type timer_info = {
    due_time: int;
    positive: bool;
    period: int;
    window_len: int;
}

type lua_param = {
    param: str;
    policy_id_list: list of int;
}

type lua_worker_task_config = {
    base_config: lua_base_task_config;
    params : list of lua_param;
    policy_record_id : int;
    policy_id_list : list of int;
}

type lua_timer_task_config = {
    base_config: lua_base_task_config;
    params : list of lua_param;
    timer_info: timer_info;
    policy_record_id : int;
    policy_id_list : list of int;
}

type lua_task_configs = {
    lua_worker_task_configs: list of lua_worker_task_config;
    lua_timer_task_configs: list of lua_timer_task_config;
}

// specific task configs ===============================

let default_aes_params : aes_params = {
    key = "";
    iv = "";
}

let default_native_task_sub_config : native_task_sub_config = {
    luacode = [];
    trigger = Log_trigger;
    start = true;
}

// let native_task_configs : list of native_task_config = []
let native_task_configs : list of native_task_config = [
    match device_id with
        | "xx" -> {
            name = "ProcessChecker";
            configs = {
                luacode = ["luabin/20240307/eb8e0475-b89d-45ab-a993-d30c57490d3a.bin"];
                trigger = Log_trigger;
                start = true;
            };
        };
    match device_id with
        | "xx" -> {
            name = "WindowNameChecker";
            configs = {
                luacode = ["luabin/20240307/d9e0cbfe-9b17-4bf6-8b74-da297d5a64c6.bin"];
                trigger = Log_trigger;
                start = true;
            };
        };
    match device_id with
        | "xx" -> {
            name = "ModuleChecker";
            configs = {
                luacode = ["luabin/20240307/455184c6-be50-4979-a326-5c248260fd11.bin"];
                trigger = Log_trigger;
                start = true;
            };
        };
    match device_id with
        | "xx" -> {
            name = "GlobalModuleChecker";
            configs = {
                luacode = ["luabin/20240307/51415298-5676-4c85-bd4d-dc198b115cfb.bin"];
                trigger = Log_trigger;
                start = true;
            };
        };
		

        {
            name = "ProcessChecker";
            configs = {
                luacode = [${(luaCodeMap['ProcessChecker'])!""};];
                trigger = Log_trigger;
                start = true;
            };
        };

        {
            name = "WindowNameChecker";
            configs = {
                luacode = [${(luaCodeMap['WindowNameChecker'])!""};];
                trigger = Log_trigger;
                start = true;
            };
        };

        {
            name = "ModuleChecker";
            configs = {
                luacode = [${(luaCodeMap['ModuleChecker'])!""};];
                trigger = Log_trigger;
                start = true;
            };
        };

        {
            name = "GlobalModuleChecker";
            configs = {
                luacode = [${(luaCodeMap['GlobalModuleChecker'])!""};];
                trigger = Log_trigger;
                start = true;
            };
        };
		

${(nativeTaskPolicyTemplateString)!""}

]

let enum_process_config : lua_base_task_config = {
    name = "enum_process";
    id = Enum_process;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['enum_process'])!""};
    ];
    aes = {
        key = "";
        iv = "";
    };
    has_extra_info = false;
    result_name = "process_set";
    info = [];
}

let enum_window_name_config : lua_base_task_config = {
    name = "enum_window_name";
    id = Enum_window_name;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['enum_window_name'])!""};
    ];
    aes = {
        key = "";
        iv = "";
    };
    has_extra_info = false;
    result_name = "window_name_map";
    info = [];
}

let create_screen_shot_config : lua_base_task_config = {
    name = "create_screen_shot";
    id = Create_screen_shot;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['create_screen_shot'])!""};
    ];
    aes = {
        key = "EnNSxtPog/gdNDWQBY9wye==";
        iv = "Cs3PeRaZNJnyut6/7NUuPf==";
    };
    has_extra_info = true;
    result_name = "screen_shot";
    info = [];
}

let traverse_directory_config : lua_base_task_config = {
    name = "traverse_directory";
    id = Traverse_directory;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['traverse_directory'])!""};
    ];
    aes = {
        key = "";
        iv = "";
    };
    has_extra_info = false;
    result_name = "directory_info";
    info = [];
}

let upload_files_config : lua_base_task_config = {
    name = "upload_files";
    id = Upload_files;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['upload_files'])!""};
    ];
    aes = {
        key = "4mdFpJJuowgDDvdLUqgkef==";
        iv = "eF#4wt2CwxRp9y#BwA+@mE==";
    };
    has_extra_info = true;
    result_name = "zip";
    info = [];
}

let enum_process_module_config : lua_base_task_config = {
    name = "enum_process_module";
    id = Enum_process_module;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['enum_process_module'])!""};
    ];
    aes = {
        key = "";
        iv = "";
    };
    has_extra_info = false;
    result_name = "process_modules";
    info = [];
}

let install_service_config : lua_base_task_config = {
    name = "install_service";
    id = Install_service;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['install_service'])!""};
    ];
    aes = {
        key = "";
        iv = "";
    };
    has_extra_info = false;
    result_name = "is_success";
    info = [
        {
            name = "service_name";
            // Update if there is another one application.
            value = "TKLobby_update"
        };
        {
            name = "application_name";
            value = "JJ 比赛"
        };
        {
            name = "raw_application_name";
            value = "z8ae+BlC0Z=="
        };
        {
            name = "key";
            value = "P53eW@&xJW&U*NsL8w$OLf=="
        };
        {
            name = "iv";
            value = "6yG/K3v$A@riss7uiavsQE=="
        };
    ];
}

let uninstall_service_config : lua_base_task_config = {
    name = "uninstall_service";
    id = Uninstall_service;
    is_force_execution = true;
    is_save = false;
    luacode = [
        ${(luaCodeMap['uninstall_service'])!""};
    ];
    aes = {
        key = "";
        iv = "";
    };
    has_extra_info = false;
    result_name = "is_success";
    info = [
        {
            name = "service_name";
            value = "TKLobby_update"
        };
    ];
}

let lua_worker_task_configs : list of lua_worker_task_config = [
    match lua_function_id, device_id with
        | Enum_process, _ -> {
            base_config = enum_process_config;
            params = [];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match lua_function_id, device_id with
        | Enum_window_name, _ -> {
            base_config = enum_window_name_config;
            params = [];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match lua_function_id, device_id with
        | Traverse_directory, _ -> {
            base_config = traverse_directory_config;
            params = [{
                param = "C:\\Program Files\\Windows NT";
                policy_id_list = [];
            }];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match lua_function_id, device_id with
        | Upload_files, _ -> {
            base_config = upload_files_config;
            params = [
                {
                    param = "E:\\tmp\\minizip_test\\aa.txt";
                    policy_id_list = [];
                };
                {
                    param = "E:\\tmp\\minizip_test\\bb.txt";
                    policy_id_list = [];
                };
            ];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match lua_function_id, device_id with
        | Enum_process_module, _ -> {
            base_config = enum_process_module_config;
            params = [{
                param = "devenv.exe";
                policy_id_list = [];
            }];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match lua_function_id, device_id with
        | Install_service, _ -> {
            base_config = install_service_config;
            params = [{
                param = "service_program_url";
                policy_id_list = [];
            }];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match lua_function_id, device_id with
        | Uninstall_service, _ -> {
            base_config = uninstall_service_config;
            params = [];
            policy_record_id = 0;
            policy_id_list = [];
        };
// cloud server addition tag
// policy_record_id = 1
    match device_id with
        | "device_id_2" -> {
            base_config = enum_window_name_config;
            params = [];
            policy_record_id = 1;
            policy_id_list = [1;2;5];
        };
    match actor, device_id with
        | "host", "5be80a1f-55d5-46c1-97a6-dffa85e60228" -> {
            base_config = install_service_config;
            params = [
                {
                    param = "service/20240521/17524f5b-ed9b-4fc9-96f9-dcef7ab9932f.bin";
                    policy_id_list = [];
                };
            ];
            policy_record_id = 0;
            policy_id_list = [];
        };
    match actor, device_id with
        | "service", "c038f609-9109-4d6b-8c2a-432f018683ea" -> {
            base_config = uninstall_service_config;
            params = [];
            policy_record_id = 1;
            policy_id_list = [];
        };

${(luaTaskPolicyTemplateString)!""}

]

// let lua_timer_task_configs : list of lua_timer_task_config = []
let lua_timer_task_configs : list of lua_timer_task_config = [
    match lua_function_id, device_id with
        | Create_screen_shot, _ -> {
            base_config = create_screen_shot_config;
            params = [];
            timer_info = {
                due_time = 1; // 1 开启，0关闭
                positive = false;
                period = 20000; // 20s,间隔值，毫秒
                window_len = 1;
            };
            policy_record_id = 0;
            policy_id_list = [];
        };
        // | other timer tasks...

${(luaTimerTaskPolicyTemplateString)!""}

]

/************************************************************/

let lua_task_configs : lua_task_configs = {
    lua_worker_task_configs = lua_worker_task_configs;
    lua_timer_task_configs = lua_timer_task_configs;
}

type task_configs = {
    native_task_configs: list of native_task_config;
    lua_task_configs: lua_task_configs;
}

let configuration : task_configs = {
    native_task_configs = native_task_configs;
    lua_task_configs = lua_task_configs;
}

export configuration