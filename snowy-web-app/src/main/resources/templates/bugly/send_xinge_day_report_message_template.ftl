<#assign appPlatformNameMap={"0":"Android","1":"iOS","3":"H5"}/>
<#assign unitNameMap={"rate_launch_total":"%","rate_user_total":"%","rate_launch_crash":"%","rate_launch_caton":"%","rate_launch_error":"%","rate_launch_report":"%","rate_launch_anr":"%","rate_user_crash":"%","rate_user_caton":"%","rate_user_error":"%","rate_user_report":"%","rate_user_anr":"%"}/>
<#if alarmDetailDTO.alarmType=='hour_ab' || alarmDetailDTO.alarmType=='day_ab'>
${"JJ Bugly 系统消息(数据异常日报)"}：
<#else>
${"JJ Bugly 日报"}：
</#if>
尊敬的用户，您好，您的产品 [${alarmDetailDTO.appInfo.appName}(${(appPlatformNameMap[alarmDetailDTO.appInfo.platform?c])!"未知应用类型"})] 于 ${timeStart} 至 ${timeEnd} 期间，触发<#if data.count??>${(data.count)!""}次<#else >多次</#if>告警，请留意！
详情请登录【MIS门户-->JJBUGLY2.0】查看
<#if alarmDetailDTO.alarmType=='hour_ab' || alarmDetailDTO.alarmType=='day_ab'>
注意：本告警消息为数据异常告警，应用类型[${(appPlatformNameMap[alarmDetailDTO.appInfo.platform?c])!"未知应用类型"}]可能存在数据异常，该统计周期内所有告警消息均不发送，请联系管理员确认！
<#else>
</#if>
此消息发送时间：${createTime}